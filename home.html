<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Dashboard - Kamusta Po Guro</title>
    <link rel="stylesheet" href="style.css?v=3.0">
</head>
<body>
    <!-- Mobile Sidebar Toggle -->
    <button class="mobile-sidebar-toggle" id="mobileSidebarToggle">☰</button>

    <!-- Sidebar Overlay for Mobile -->
    <div class="sidebar-overlay" id="sidebarOverlay"></div>

    <div class="admin-layout">
        <!-- Sidebar Navigation -->
        <aside class="sidebar" id="sidebar">
            <div class="sidebar-header">
                <div class="sidebar-logo">📚</div>
                <div class="sidebar-title">Admin Portal</div>
                <button class="mobile-sidebar-close" id="mobileSidebarClose">✕</button>
            </div>
            <nav>
                <ul class="sidebar-nav">
                    <li class="sidebar-nav-item">
                        <a href="#" class="sidebar-nav-link active">
                            <span class="sidebar-nav-icon">👥</span>
                            Teachers
                        </a>
                    </li>
                    <li class="sidebar-nav-item">
                        <a href="createAccount.html" class="sidebar-nav-link">
                            <span class="sidebar-nav-icon">➕</span>
                            Create Account
                        </a>
                    </li>
                    <li class="sidebar-nav-item">
                        <a href="#" class="sidebar-nav-link" id="settingsLink">
                            <span class="sidebar-nav-icon">⚙️</span>
                            Settings
                        </a>
                    </li>
                </ul>

                <!-- Download Game Button at Bottom -->
                <div class="sidebar-bottom">
                    <div class="sidebar-nav-item download-game-item">
                        <button id="downloadGameBtn" class="sidebar-nav-link download-game-btn">
                            <span class="sidebar-nav-icon">📱</span>
                            Download Game
                            <span class="dropdown-arrow">▼</span>
                        </button>
                        <div id="downloadDropdown" class="download-dropdown">
                            <a href="#" id="downloadAndroid" class="download-option">
                                <span class="platform-icon">🤖</span>
                                <div class="platform-info">
                                    <span class="platform-name">Android</span>
                                    <span class="platform-desc">APK File</span>
                                </div>
                            </a>
                            <a href="#" id="downloadWindows" class="download-option">
                                <span class="platform-icon">🖥️</span>
                                <div class="platform-info">
                                    <span class="platform-name">Windows</span>
                                    <span class="platform-desc">EXE File</span>
                                </div>
                            </a>
                        </div>
                    </div>
                </div>
            </nav>
        </aside>

        <!-- Main Content -->
        <main class="main-content">
            <!-- Teacher List View -->
            <div id="teacherListView" class="content-view active">
                <!-- Header -->
                <header class="admin-header">
                    <div class="header-content">
                        <h1 class="header-title">Teachers</h1>
                        <div class="header-search-wide">
                            <input type="text" id="searchTeacherInput" placeholder="Search by Name, Email, or ID...">
                        </div>
                    </div>
                </header>

                <!-- Data Table -->
                <div class="data-table-container">
                    <div class="data-table-header">
                        <h2 class="data-table-title">Teachers</h2>
                    </div>

                    <div id="teacherTableContainer">
                        <table class="data-table" id="teachersTable">
                            <thead>
                                <tr>
                                    <th>Teacher Name</th>
                                    <th>Username</th>
                                    <th>Email</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody id="teacherTableBody">
                                <!-- Teacher rows will be dynamically inserted here -->
                                <tr>
                                    <td colspan="4" style="text-align: center; padding: 2rem; color: var(--text-muted);">
                                        <span id="loadingMessage">Loading teachers...</span>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- Teacher Detail View -->
            <div id="teacherDetailView" class="content-view">
                <!-- Header -->
                <header class="admin-header">
                    <div class="header-content">
                        <button id="backToTeachersButton" class="nav-button primary">← Back</button>
                        <h1 class="header-title" id="teacherDetailTitle">Teacher Details</h1>
                        <button id="exportCsvButton" class="nav-button secondary">📊 Export CSV</button>
                    </div>
                </header>

                <!-- Teacher Detail Content -->
                <div class="teacher-detail-container">

                    <!-- Teacher Info Section -->
                    <div id="teacherInfoSection" class="detail-section desktop-visible" data-content="teacher-info">
                        <div class="detail-card teacher-info-compact">
                            <h2>
                                Teacher Information
                                <button class="teacher-info-toggle" id="teacherInfoToggle">Expand</button>
                            </h2>
                            <div id="teacherDetailInfo" class="detail-info-grid teacher-info-content collapsed">
                                <!-- Teacher info will be populated here -->
                            </div>
                        </div>
                    </div>

                    <!-- Students Section -->
                    <div id="studentsSection" class="detail-section desktop-visible active" data-content="students">
                        <div class="detail-card">
                            <h2 id="studentListTitle">Students</h2>
                            <div class="search-container">
                                <input type="text" id="searchStudentInput" placeholder="Search students by name, email, or ID...">
                            </div>
                            <div id="studentList" class="detail-student-list">
                                <p class="loading-text">Loading student data...</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- Settings Modal -->
    <div id="settingsModal" class="modal-overlay" style="display: none;">
        <div class="modal-content settings-modal">
            <button id="closeSettingsButton" class="modal-close-btn">Close</button>
            <h2>Settings</h2>

            <div class="settings-content">
                <div class="settings-section">
                    <h3>Account</h3>
                    <div class="settings-item">
                        <div class="settings-item-info">
                            <div class="settings-item-title">Logout</div>
                            <div class="settings-item-description">Sign out of your admin account</div>
                        </div>
                        <button id="logoutButton" class="btn-danger">Logout</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal Structure for Teacher Info -->
    <div id="teacherInfoModal" class="modal-overlay" style="display: none;">
        <div class="modal-content">
            <button id="closeModalButton" class="modal-close-btn">[close]</button>
            <h2 id="modalTeacherName">Teacher Details</h2>

            <!-- Mobile Navigation Buttons -->
            <div class="mobile-modal-nav">
                <button class="mobile-nav-btn active" data-tab="students">👥 Students</button>
                <button class="mobile-nav-btn" data-tab="teacher-info">ℹ️ Teacher Info</button>
            </div>

            <!-- Teacher Info Section -->
            <div id="teacherInfoTab" class="mobile-tab-content" data-content="teacher-info">
                <div id="modalTeacherInfo" class="modal-info-grid">
                    <!-- Basic info will be populated here -->
                </div>
            </div>

            <!-- Students Section -->
            <div id="studentsTab" class="mobile-tab-content active" data-content="students">
                <!-- REMOVED Teacher's Own Subcollection Section -->
                <h3 id="modalStudentListTitle">Students</h3>
                <div class="search-container">
                    <input type="text" id="searchStudentInput" placeholder="Search students by name, email, or ID...">
                </div>
                <div id="modalStudentList" class="modal-student-list">
                     <p class="loading-text">Loading student data...</p>
                </div>
            </div>
        </div>
    </div>
    <!-- End Modal Structure -->

    <!-- Student Details Modal -->
    <div id="studentDetailsModal" class="student-details-modal">
        <div class="student-modal-content">
            <div class="student-modal-header">
                <h3 id="studentModalTitle">Student Details</h3>
                <button id="studentModalClose" class="student-modal-close">Close</button>
            </div>
            <div id="studentModalBody" class="student-modal-body">
                <!-- Student details will be populated here -->
            </div>
        </div>
    </div>

    <script src="home.js"></script>
</body>
</html>
