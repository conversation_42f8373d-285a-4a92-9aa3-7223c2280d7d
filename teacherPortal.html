<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Teacher <PERSON> - Kamus<PERSON> Guro</title>
    <link rel="stylesheet" href="style.css">
    <!-- SheetJS library for Excel export -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>
</head>
<body>
    <!-- Mobile Sidebar Toggle -->
    <button class="mobile-sidebar-toggle" id="mobileSidebarToggle">☰</button>

    <!-- Sidebar Overlay for Mobile -->
    <div class="sidebar-overlay" id="sidebarOverlay"></div>

    <div class="admin-layout">
        <!-- Sidebar Navigation -->
        <aside class="sidebar" id="sidebar">
            <div class="sidebar-header">
                <div class="sidebar-logo">📚</div>
                <div class="sidebar-title">Teacher Portal</div>
                <button class="mobile-sidebar-close" id="mobileSidebarClose">✕</button>
            </div>
            <nav>
                <ul class="sidebar-nav">
                    <li class="sidebar-nav-item">
                        <a href="#" class="sidebar-nav-link active" id="studentsLink">
                            <span class="sidebar-nav-icon">👥</span>
                            My Students
                        </a>
                    </li>
                    <li class="sidebar-nav-item" id="createStudentItem">
                        <a href="#" class="sidebar-nav-link" id="createStudentLink">
                            <span class="sidebar-nav-icon">➕</span>
                            Add Student
                        </a>
                    </li>
                    <li class="sidebar-nav-item">
                        <a href="#" class="sidebar-nav-link" id="questionsLink">
                            <span class="sidebar-nav-icon">❓</span>
                            Questions
                        </a>
                    </li>
                    <li class="sidebar-nav-item">
                        <a href="#" class="sidebar-nav-link" id="levelsLink">
                            <span class="sidebar-nav-icon">🏆</span>
                            Levels
                        </a>
                    </li>
                    <li class="sidebar-nav-item">
                        <a href="#" class="sidebar-nav-link" id="sectionsLink">
                            <span class="sidebar-nav-icon">📋</span>
                            Sections
                        </a>
                    </li>
                    <li class="sidebar-nav-item">
                        <a href="#" class="sidebar-nav-link" id="settingsLink">
                            <span class="sidebar-nav-icon">⚙️</span>
                            Settings
                        </a>
                    </li>
                </ul>
            </nav>
        </aside>

        <!-- Main Content -->
        <main class="main-content">
            <!-- Student List View -->
            <div id="studentListView" class="content-view active">
                <header class="admin-header">
                    <div class="header-content">
                        <h1 class="header-title">My Students</h1>
                        <div class="header-search-wide">
                            <input type="text" id="searchStudentInput" placeholder="Search by Name or ID...">
                        </div>
                        <div class="header-actions">
                            <select id="sectionFilter" class="section-filter">
                                <option value="">All Sections</option>
                            </select>
                            <button id="exportExcelButton" class="button secondary">📊 Export Excel Report</button>
                            <button id="logoutButton" class="button danger">Logout</button>
                        </div>
                    </div>
                </header>
                
                <div class="content-container">
                    <div id="loadingMessage" class="loading-message">Loading students...</div>
                    <div class="table-container">
                        <table class="data-table">
                            <thead>
                                <tr>
                                    <th class="sortable" data-sort="fullname">
                                        Student Name <span class="sort-indicator"></span>
                                    </th>
                                    <th class="sortable" data-sort="username">
                                        Username <span class="sort-indicator"></span>
                                    </th>
                                    <th class="sortable" data-sort="section">
                                        Section <span class="sort-indicator"></span>
                                    </th>
                                    <th class="sortable" data-sort="progress">
                                        Progress <span class="sort-indicator"></span>
                                    </th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody id="studentTableBody">
                                <!-- Student rows will be populated here -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- Questions View -->
            <div id="questionsView" class="content-view">
                <!-- Questions content will go here -->
                <header class="admin-header">
                    <div class="header-content">
                        <h1 class="header-title">Questions</h1>
                        <div class="header-actions">
                            <button id="addQuestionButton" class="button primary">Add Question</button>
                        </div>
                    </div>
                </header>
                
                <div class="content-container">
                    <div class="level-tabs" id="levelTabs">
                        <!-- Level tabs will be generated here -->
                    </div>
                    <div class="questions-container" id="questionsContainer">
                        <!-- Questions will be loaded here -->
                    </div>
                </div>
            </div>

            <!-- Levels View -->
            <div id="levelsView" class="content-view">
                <header class="admin-header">
                    <div class="header-content">
                        <h1 class="header-title">Level Management</h1>
                        <div class="header-actions">
                            <button id="saveLevelChanges" class="button primary">Save Changes</button>
                        </div>
                    </div>
                </header>

                <div class="content-container">
                    <div id="levelsContainer" class="levels-container">
                        <div id="loadingLevels" class="loading-message">Loading levels...</div>
                        <div id="levelUnlockGrid" class="levels-grid">
                            <!-- Level cards will be generated here -->
                        </div>
                    </div>
                </div>
            </div>

            <!-- Sections View -->
            <div id="sectionsView" class="content-view">
                <header class="admin-header">
                    <div class="header-content">
                        <h1 class="header-title">Section Management</h1>
                        <div class="header-actions">
                            <button id="addSectionButton" class="button primary">Add Section</button>
                        </div>
                    </div>
                </header>

                <div class="content-container">
                    <div id="sectionsContainer" class="sections-container">
                        <div id="loadingSections" class="loading-message">Loading sections...</div>
                        <div id="sectionsGrid" class="sections-grid">
                            <!-- Sections will be loaded here -->
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- Settings Modal - Completely Isolated -->
    <div id="settingsModal" class="settings-overlay" style="display: none;">
        <div class="settings-container">
            <button id="closeSettingsButton" class="settings-close-btn">Close</button>
            <h2>Settings</h2>

            <div class="settings-content">
                <div class="settings-section">
                    <h3>Account</h3>
                    <div class="settings-item">
                        <div class="settings-item-info">
                            <div class="settings-item-title">Logout</div>
                            <div class="settings-item-description">Sign out of your teacher account</div>
                        </div>
                        <button id="settingsLogoutButton" class="btn-danger">Logout</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Add Student Modal - Completely Isolated -->
    <div id="addStudentModal" class="add-student-overlay" style="display: none;">
        <div class="add-student-container">
            <button id="closeAddStudentButton" class="add-student-close-btn">Close</button>
            <h2>Create Student Accounts</h2>

            <div class="add-student-form">
                <div class="input-wrapper">
                    <label for="sectionSelectInput">Select Section:</label>
                    <select id="sectionSelectInput" class="section-select">
                        <option value="">Select a section</option>
                    </select>
                </div>
                <div class="input-wrapper">
                    <label for="studentCountInput">Number of Accounts:</label>
                    <input type="number" id="studentCountInput" placeholder="Input Number" min="1" max="50" />
                </div>
                <button id="createAccountsButton" class="create-btn">Create</button>
                <div id="addStudentError" class="add-student-error" style="display: none;"></div>
            </div>
        </div>
    </div>

    <!-- Creating Accounts Progress Modal - Completely Isolated -->
    <div id="creatingAccountsModal" class="progress-overlay" style="display: none;">
        <div class="progress-container">
            <h2>Now Creating the Accounts</h2>
            <p>Please Do not close the Web</p>

            <div class="progress-wrapper">
                <div class="progress-status">
                    <span id="progressText">Created: 0 / 0 (Checking: 0)</span>
                </div>
                <div class="progress-track">
                    <div id="progressFill" class="progress-bar-fill"></div>
                </div>
                <button id="cancelCreationButton" class="cancel-btn">Cancel the Remaining Account</button>
            </div>
        </div>
    </div>

    <!-- Add Section Modal -->
    <div id="addSectionModal" class="section-modal-overlay" style="display: none;">
        <div class="section-modal-container">
            <button id="closeAddSectionButton" class="section-modal-close-btn">Close</button>
            <h2>Add New Section</h2>

            <div class="section-form">
                <div class="input-wrapper">
                    <label for="sectionNameInput">Section Name:</label>
                    <input type="text" id="sectionNameInput" placeholder="Enter section name" maxlength="50" />
                </div>
                <div class="input-wrapper">
                    <label for="sectionDescriptionInput">Description (Optional):</label>
                    <textarea id="sectionDescriptionInput" placeholder="Enter section description" maxlength="200"></textarea>
                </div>
                <button id="createSectionButton" class="create-btn">Create Section</button>
                <div id="addSectionError" class="section-error" style="display: none;"></div>
            </div>
        </div>
    </div>

    <!-- Edit Section Modal -->
    <div id="editSectionModal" class="section-modal-overlay" style="display: none;">
        <div class="section-modal-container">
            <button id="closeEditSectionButton" class="section-modal-close-btn">Close</button>
            <h2>Edit Section</h2>

            <div class="section-form">
                <div class="input-wrapper">
                    <label for="editSectionNameInput">Section Name:</label>
                    <input type="text" id="editSectionNameInput" placeholder="Enter section name" maxlength="50" />
                </div>
                <div class="input-wrapper">
                    <label for="editSectionDescriptionInput">Description (Optional):</label>
                    <textarea id="editSectionDescriptionInput" placeholder="Enter section description" maxlength="200"></textarea>
                </div>
                <button id="updateSectionButton" class="create-btn">Update Section</button>
                <div id="editSectionError" class="section-error" style="display: none;"></div>
            </div>
        </div>
    </div>

    <script src="js/inputSanitizer.js"></script>
    <script src="teacherPortal.js"></script>
</body>
</html>