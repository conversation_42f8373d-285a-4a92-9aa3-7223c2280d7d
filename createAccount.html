<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Create Teacher Account - <PERSON><PERSON><PERSON></title>
    <link rel="stylesheet" href="style.css?v=3.0">
</head>
<body>
    <!-- Mobile Sidebar Toggle -->
    <button class="mobile-sidebar-toggle" id="mobileSidebarToggle">☰</button>

    <!-- Sidebar Overlay for Mobile -->
    <div class="sidebar-overlay" id="sidebarOverlay"></div>

    <div class="admin-layout">
        <!-- Sidebar Navigation -->
        <aside class="sidebar" id="sidebar">
            <div class="sidebar-header">
                <div class="sidebar-logo">📚</div>
                <div class="sidebar-title">Admin Portal</div>
                <button class="mobile-sidebar-close" id="mobileSidebarClose">✕</button>
            </div>
            <nav>
                <ul class="sidebar-nav">
                    <li class="sidebar-nav-item">
                        <a href="home.html" class="sidebar-nav-link">
                            <span class="sidebar-nav-icon">👥</span>
                            Teachers
                        </a>
                    </li>
                    <li class="sidebar-nav-item">
                        <a href="#" class="sidebar-nav-link active">
                            <span class="sidebar-nav-icon">➕</span>
                            Create Account
                        </a>
                    </li>
                    <li class="sidebar-nav-item">
                        <a href="#" class="sidebar-nav-link" id="settingsLink">
                            <span class="sidebar-nav-icon">⚙️</span>
                            Settings
                        </a>
                    </li>
                </ul>

                <!-- Download Game Button at Bottom -->
                <div class="sidebar-bottom">
                    <div class="sidebar-nav-item download-game-item">
                        <button id="downloadGameBtn" class="sidebar-nav-link download-game-btn">
                            <span class="sidebar-nav-icon">📱</span>
                            Download Game
                            <span class="dropdown-arrow">▼</span>
                        </button>
                        <div id="downloadDropdown" class="download-dropdown">
                            <a href="#" id="downloadAndroid" class="download-option">
                                <span class="platform-icon">🤖</span>
                                <div class="platform-info">
                                    <span class="platform-name">Android</span>
                                    <span class="platform-desc">APK File</span>
                                </div>
                            </a>
                            <a href="#" id="downloadWindows" class="download-option">
                                <span class="platform-icon">🖥️</span>
                                <div class="platform-info">
                                    <span class="platform-name">Windows</span>
                                    <span class="platform-desc">EXE File</span>
                                </div>
                            </a>
                        </div>
                    </div>
                </div>
            </nav>
        </aside>

        <!-- Main Content -->
        <main class="main-content">
            <!-- Header -->
            <header class="admin-header">
                <div class="header-left">
                    <h1 class="header-title">Create Teacher Account</h1>
                </div>
            </header>

            <!-- Create Account Form -->
            <div class="data-table-container">
                <div class="data-table-header">
                    <h2 class="data-table-title">New Teacher Account</h2>
                </div>

                <div class="create-account-form-container">
                    <form id="signUpForm" class="create-account-form">
                        <div class="form-row">
                            <div class="form-group">
                                <label for="fullName">Full Name</label>
                                <input type="text" id="fullName" placeholder="Enter full name" required>
                            </div>
                            <div class="form-group">
                                <label for="email">Email Address</label>
                                <input type="email" id="email" placeholder="Enter email address" required>
                            </div>
                        </div>

                        <div class="form-row">
                            <div class="form-group">
                                <label for="username">Username</label>
                                <input type="text" id="username" placeholder="Enter username" required>
                            </div>
                            <div class="form-group">
                                <label for="id">Teacher ID</label>
                                <input type="text" id="id" placeholder="Enter teacher ID" required>
                            </div>
                        </div>

                        <div class="form-row">
                            <div class="form-group">
                                <label for="password">Password</label>
                                <input type="password" id="password" placeholder="Enter password" required>
                            </div>
                            <div class="form-group">
                                <label for="rePassword">Confirm Password</label>
                                <input type="password" id="rePassword" placeholder="Confirm password" required>
                            </div>
                        </div>

                        <div class="form-actions">
                            <button type="button" class="btn-secondary" onclick="window.location.href='home.html'">
                                Cancel
                            </button>
                            <button type="submit" id="signUpButton" class="btn-primary">
                                Create Account
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </main>
    </div>

    <!-- Settings Modal -->
    <div id="settingsModal" class="modal-overlay" style="display: none;">
        <div class="modal-content settings-modal">
            <button id="closeSettingsButton" class="modal-close-btn">Close</button>
            <h2>Settings</h2>

            <div class="settings-content">
                <div class="settings-section">
                    <h3>Account</h3>
                    <div class="settings-item">
                        <div class="settings-item-info">
                            <div class="settings-item-title">Logout</div>
                            <div class="settings-item-description">Sign out of your admin account</div>
                        </div>
                        <button id="logoutButton" class="btn-danger">Logout</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="createAccount.js"></script>
</body>
</html>