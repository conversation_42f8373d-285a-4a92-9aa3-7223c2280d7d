/* === Google Fonts Import === */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Roboto:wght@300;400;500&display=swap');

/* === Modern Admin Dashboard Theme === */
:root {
    /* Main Color Palette */
    --primary-bg: #f8fafc;
    --secondary-bg: #ffffff;
    --sidebar-bg: #1e293b;
    --card-bg: #ffffff;
    --accent-yellow: #fbbf24;
    --accent-yellow-light: #fde68a;
    --accent-yellow-dark: #f59e0b;

    /* Text Colors */
    --text-primary: #0f172a;
    --text-secondary: #64748b;
    --text-muted: #94a3b8;
    --text-white: #ffffff;
    --text-link: #3b82f6;

    /* Action Colors */
    --btn-primary: #fbbf24;
    --btn-primary-hover: #f59e0b;
    --btn-secondary: #f1f5f9;
    --btn-secondary-hover: #e2e8f0;
    --btn-danger: #ef4444;
    --btn-danger-hover: #dc2626;
    --btn-success: #10b981;
    --btn-success-hover: #059669;

    /* Borders */
    --border-light: #e2e8f0;
    --border-medium: #cbd5e1;
    --border-focus: #3b82f6;

    /* Shadows */
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);

    /* Typography */
    --font-primary: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
    --font-weight-light: 300;
    --font-weight-normal: 400;
    --font-weight-medium: 500;
    --font-weight-semibold: 600;
    --font-weight-bold: 700;

    /* Spacing */
    --space-1: 4px;
    --space-2: 8px;
    --space-3: 12px;
    --space-4: 16px;
    --space-5: 20px;
    --space-6: 24px;
    --space-8: 32px;
    --space-10: 40px;
    --space-12: 48px;
    --space-16: 64px;

    /* Border Radius */
    --radius-sm: 6px;
    --radius-md: 8px;
    --radius-lg: 12px;
    --radius-xl: 16px;

    /* Transitions */
    --transition: 200ms cubic-bezier(0.4, 0.0, 0.2, 1);
    --transition-fast: 150ms cubic-bezier(0.4, 0.0, 0.2, 1);
    --transition-slow: 300ms cubic-bezier(0.4, 0.0, 0.2, 1);
    --transition-bounce: 400ms cubic-bezier(0.68, -0.55, 0.265, 1.55);
    --transition-smooth: 250ms cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

/* === Global Reset === */
* {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
}

html {
    font-size: 16px;
    line-height: 1.6;
    scroll-behavior: smooth;
}

body {
    background: var(--primary-bg);
    color: var(--text-primary);
    font-family: var(--font-primary);
    font-weight: var(--font-weight-normal);
    min-height: 100vh;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    margin: 0;
    padding: 0;
    opacity: 0;
    animation: fadeIn 0.2s ease-out forwards;
}

/* === Typography === */
h1, h2, h3, h4, h5, h6 {
    font-weight: var(--font-weight-medium);
    line-height: 1.3;
    color: var(--text-primary);
}

h1 { font-size: 2rem; }
h2 { font-size: 1.5rem; }
h3 { font-size: 1.25rem; }
h4 { font-size: 1.125rem; }

p {
    line-height: 1.6;
    color: var(--text-secondary);
}

/* === Focus Styles === */
*:focus {
    outline: 2px solid var(--border-focus);
    outline-offset: 2px;
}

*:focus:not(:focus-visible) {
    outline: none;
}

button:focus,
input:focus,
select:focus,
textarea:focus {
    outline: 2px solid var(--border-focus);
    outline-offset: 2px;
}
/* === Admin Dashboard Layout === */
.admin-layout {
    display: flex;
    min-height: 100vh;
    background: var(--primary-bg);
    position: relative;
    width: 100%;
    box-sizing: border-box;
}

/* === Sidebar === */
.sidebar {
    width: 280px;
    background: var(--sidebar-bg);
    color: var(--text-white);
    padding: var(--space-6);
    box-shadow: var(--shadow-lg);
    position: fixed;
    height: 100vh;
    overflow-y: auto;
    z-index: 100;
    top: 0;
    left: 0;
    display: flex;
    flex-direction: column;
    opacity: 0;
    animation: fadeIn 0.4s ease-out forwards;
}

.sidebar-header {
    display: flex;
    align-items: center;
    gap: var(--space-3);
    padding-bottom: var(--space-6);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    margin-bottom: var(--space-6);
}

.sidebar-logo {
    width: 40px;
    height: 40px;
    background: var(--accent-yellow);
    border-radius: var(--radius-md);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    font-weight: var(--font-weight-bold);
    color: var(--text-primary);
}

.sidebar-title {
    font-size: 1.25rem;
    font-weight: var(--font-weight-semibold);
    color: var(--text-white);
}

.sidebar-nav {
    list-style: none;
    padding: 0;
    margin: 0;
    flex: 1;
}

.sidebar-nav-item {
    opacity: 0;
    animation: fadeIn 0.3s ease-out forwards;
}

.sidebar-nav-item:nth-child(1) { animation-delay: 0.4s; }
.sidebar-nav-item:nth-child(2) { animation-delay: 0.45s; }
.sidebar-nav-item:nth-child(3) { animation-delay: 0.5s; }
.sidebar-nav-item:nth-child(4) { animation-delay: 0.55s; }
.sidebar-nav-item:nth-child(5) { animation-delay: 0.6s; }

/* Sidebar Bottom Section */
.sidebar-bottom {
    margin-top: auto;
    padding-top: var(--space-4);
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.sidebar-nav-item {
    margin-bottom: var(--space-2);
}

.sidebar-nav-link {
    display: flex;
    align-items: center;
    gap: var(--space-3);
    padding: var(--space-3) var(--space-4);
    color: var(--text-muted);
    text-decoration: none;
    border-radius: var(--radius-md);
    transition: all var(--transition-smooth);
    font-weight: var(--font-weight-medium);
    position: relative;
    overflow: hidden;
}

.sidebar-nav-link::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    transition: left var(--transition-slow);
}

.sidebar-nav-link:hover::before {
    left: 100%;
}

.sidebar-nav-link:hover,
.sidebar-nav-link.active {
    background: rgba(255, 255, 255, 0.1);
    color: var(--text-white);
    transform: translateX(4px);
}

.sidebar-nav-link.active {
    background: var(--accent-yellow);
    color: var(--text-primary);
    box-shadow: 0 2px 8px rgba(251, 191, 36, 0.3);
}

.sidebar-nav-icon {
    font-size: 1.25rem;
    width: 24px;
    text-align: center;
}

/* Download Game Button */
.download-game-item {
    position: relative;
}

.download-game-btn {
    justify-content: space-between;
    position: relative;
    border: none;
    background: none;
    width: 100%;
    text-align: left;
    cursor: pointer;
}

.dropdown-arrow {
    font-size: 0.8em;
    transition: transform 0.2s ease;
    margin-left: auto;
}

.download-game-btn:hover .dropdown-arrow {
    transform: rotate(180deg);
}

.download-dropdown {
    position: absolute;
    bottom: 100%;
    left: 0;
    right: 0;
    background: var(--sidebar-bg);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: var(--radius-md);
    box-shadow: var(--shadow-lg);
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    transform: translateY(10px);
    transition: all 0.2s ease;
    margin-bottom: var(--space-1);
    overflow: hidden;
}

.download-dropdown.show {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.download-option {
    display: flex;
    align-items: center;
    padding: var(--space-3);
    color: var(--text-muted);
    text-decoration: none;
    transition: all 0.2s ease;
    border-bottom: 1px solid rgba(255, 255, 255, 0.05);
}

.download-option:last-child {
    border-bottom: none;
}

.download-option:hover {
    background: rgba(255, 255, 255, 0.1);
    color: var(--text-white);
}

.platform-icon {
    font-size: 1.5em;
    margin-right: var(--space-3);
}

.platform-info {
    display: flex;
    flex-direction: column;
}

.platform-name {
    font-weight: var(--font-weight-semibold);
    color: inherit;
    font-size: 0.9em;
}

.platform-desc {
    font-size: 0.75em;
    color: var(--text-muted);
    margin-top: 2px;
}

/* === Main Content Area === */
.main-content {
    flex: 1;
    margin-left: 280px;
    padding: var(--space-6);
    background: var(--primary-bg);
    min-height: 100vh;
    width: calc(100% - 280px);
    box-sizing: border-box;
    position: relative;
    opacity: 0;
    animation: fadeIn 0.4s ease-out 0.2s forwards;
}

/* === Header === */
.admin-header {
    background: var(--secondary-bg);
    padding: var(--space-4) var(--space-6);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-sm);
    margin-bottom: var(--space-6);
    display: flex;
    justify-content: space-between;
    align-items: center;
    border: 1px solid var(--border-light);
}

.header-content {
    display: flex;
    align-items: center;
    gap: var(--space-6);
    width: 100%;
}

.header-title {
    font-size: 1.5rem;
    font-weight: var(--font-weight-semibold);
    color: var(--text-primary);
    margin: 0;
    flex-shrink: 0;
}

.header-search-wide {
    position: relative;
    flex: 1;
    max-width: 600px;
}

.header-search input,
.header-search-wide input {
    width: 100%;
    padding: var(--space-3) var(--space-4);
    border: 1px solid var(--border-light);
    border-radius: var(--radius-md);
    background: var(--secondary-bg);
    font-size: 0.875rem;
    color: var(--text-primary);
    transition: all var(--transition);
}

.header-search input:focus,
.header-search-wide input:focus {
    outline: none;
    border-color: var(--border-focus);
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.header-actions {
    display: flex;
    align-items: center;
    gap: var(--space-3);
    flex-shrink: 0;
}

.section-filter {
    padding: var(--space-2) var(--space-3);
    border: 1px solid var(--border-light);
    border-radius: var(--radius-md);
    background: var(--secondary-bg);
    color: var(--text-primary);
    font-size: 0.875rem;
    min-width: 140px;
}

.header-search-icon {
    position: absolute;
    left: var(--space-3);
    top: 50%;
    transform: translateY(-50%);
    color: var(--text-muted);
    font-size: 1rem;
}

.header-right {
    display: flex;
    align-items: center;
    gap: var(--space-4);
}

.header-user {
    display: flex;
    align-items: center;
    gap: var(--space-3);
    padding: var(--space-2) var(--space-3);
    border-radius: var(--radius-md);
    background: var(--primary-bg);
    border: 1px solid var(--border-light);
}

.header-user-avatar {
    width: 32px;
    height: 32px;
    background: var(--accent-yellow);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: var(--font-weight-semibold);
    color: var(--text-primary);
    font-size: 0.875rem;
}

.header-user-info {
    display: flex;
    flex-direction: column;
}

.header-user-name {
    font-size: 0.875rem;
    font-weight: var(--font-weight-medium);
    color: var(--text-primary);
    line-height: 1.2;
}

.header-user-role {
    font-size: 0.75rem;
    color: var(--text-secondary);
    line-height: 1.2;
}

/* === Modern Login Page === */
.login-page {
    min-height: 100vh;
    background: linear-gradient(135deg, var(--primary-bg) 0%, #e2e8f0 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    padding: var(--space-6);
    opacity: 0;
    animation: fadeIn 0.4s ease-out forwards;
}

.login-container {
    background: var(--secondary-bg);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-xl);
    padding: var(--space-10);
    width: 100%;
    max-width: 420px;
    border: 1px solid var(--border-light);
    opacity: 0;
    transform: translateY(20px);
    animation: fadeInUp 0.5s ease-out 0.2s forwards;
}

.login-header {
    text-align: center;
    margin-bottom: var(--space-8);
}

.login-logo {
    width: 64px;
    height: 64px;
    background: var(--accent-yellow);
    border-radius: var(--radius-xl);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2rem;
    margin: 0 auto var(--space-4);
    box-shadow: var(--shadow-md);
    opacity: 0;
    animation: fadeIn 0.4s ease-out 0.4s forwards;
    transition: transform var(--transition-fast), box-shadow var(--transition-fast);
}

.login-logo:hover {
    transform: scale(1.05);
    box-shadow: var(--shadow-lg);
}

.login-header h1 {
    font-size: 1.75rem;
    font-weight: var(--font-weight-bold);
    color: var(--text-primary);
    margin: 0 0 var(--space-2) 0;
}

.login-subtitle {
    color: var(--text-secondary);
    font-size: 0.875rem;
    margin: 0;
}

.login-form-container h2 {
    font-size: 1.5rem;
    font-weight: var(--font-weight-semibold);
    color: var(--text-primary);
    margin: 0 0 var(--space-2) 0;
    text-align: center;
}

.login-description {
    color: var(--text-secondary);
    font-size: 0.875rem;
    text-align: center;
    margin: 0 0 var(--space-8) 0;
}

.login-form {
    display: flex;
    flex-direction: column;
    gap: var(--space-6);
}

.form-group {
    display: flex;
    flex-direction: column;
    gap: var(--space-2);
}

.form-group label {
    font-size: 0.875rem;
    font-weight: var(--font-weight-medium);
    color: var(--text-primary);
}

.form-group {
    opacity: 0;
    transform: translateY(15px);
    animation: fadeInUp 0.4s ease-out forwards;
}

.form-group:nth-child(1) { animation-delay: 0.6s; }
.form-group:nth-child(2) { animation-delay: 0.7s; }
.form-group:nth-child(3) { animation-delay: 0.8s; }

.form-group input {
    padding: var(--space-4);
    border: 1px solid var(--border-light);
    border-radius: var(--radius-md);
    background: var(--secondary-bg);
    font-size: 1rem;
    color: var(--text-primary);
    transition: all var(--transition-smooth);
    position: relative;
}

.form-group input:focus {
    outline: none;
    border-color: var(--border-focus);
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
    transform: translateY(-1px);
}

.form-group input:hover {
    border-color: var(--border-medium);
    transform: translateY(-1px);
}

.login-button {
    width: 100%;
    margin-top: var(--space-4);
    opacity: 0;
    transform: translateY(15px);
    animation: fadeInUp 0.4s ease-out 0.9s forwards;
}

/* === Legacy Layout (for compatibility) === */
.container,
.home-container,
.info-page-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: var(--space-8);
    background: var(--secondary-bg);
    min-height: 100vh;
}

/* Login Page */
.container {
    max-width: 400px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    gap: var(--space-8);
    padding: var(--space-10);
    margin-top: 0;
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-1);
    margin-top: 10vh;
    min-height: auto;
}

.admin-login-container {
    max-width: 360px;
}

/* Dashboard */
.home-container,
.info-page-container {
    padding: var(--space-6);
    background: var(--primary-bg);
    box-shadow: none;
    border-radius: 0;
}


/* === Forms === */
.form-container {
    width: 100%;
    display: flex;
    flex-direction: column;
    gap: var(--space-6);
}

h1 {
    font-size: 1.75rem;
    font-weight: var(--font-weight-medium);
    color: var(--text-primary);
    text-align: center;
    margin-bottom: var(--space-2);
}

.subtitle {
    font-size: 0.875rem;
    color: var(--text-secondary);
    text-align: center;
    margin-bottom: var(--space-6);
}

.input-row {
    display: flex;
    flex-direction: column;
    gap: var(--space-4);
    margin-bottom: var(--space-6);
}

/* === Inputs === */
input[type="email"],
input[type="password"],
input[type="text"],
#searchTeacherInput {
    width: 100%;
    padding: var(--space-4);
    background: var(--secondary-bg);
    border: 1px solid var(--border-light);
    border-radius: var(--radius-md);
    color: var(--text-primary);
    font-size: 1rem;
    font-family: var(--font-google);
    transition: all var(--transition);
    box-sizing: border-box;
}

input:focus {
    outline: none;
    border-color: var(--border-focus);
    box-shadow: 0 0 0 3px rgba(26, 115, 232, 0.1);
}

input::placeholder {
    color: var(--text-muted);
}

/* === Modern Buttons === */
.buttons {
    display: flex;
    gap: var(--space-3);
    margin-top: var(--space-6);
}

button,
.nav-button,
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: var(--space-2);
    padding: var(--space-3) var(--space-4);
    border: 1px solid transparent;
    border-radius: var(--radius-md);
    font-family: var(--font-primary);
    font-size: 0.875rem;
    font-weight: var(--font-weight-medium);
    text-decoration: none;
    cursor: pointer;
    transition: all var(--transition);
    min-height: 40px;
    white-space: nowrap;
    position: relative;
    overflow: hidden;
}

/* Primary Button - Yellow */
button.primary,
.nav-button.primary,
.btn-primary {
    background: var(--btn-primary);
    color: var(--text-primary);
    border-color: var(--btn-primary);
    box-shadow: var(--shadow-sm);
    position: relative;
    overflow: hidden;
}

.btn-primary::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left var(--transition-slow);
}

.btn-primary:hover::before {
    left: 100%;
}

button.primary:hover,
.nav-button.primary:hover,
.btn-primary:hover {
    background: var(--btn-primary-hover);
    border-color: var(--btn-primary-hover);
    box-shadow: var(--shadow-md);
    transform: translateY(-2px) scale(1.02);
}

.btn-primary:active {
    transform: translateY(0) scale(0.98);
    transition: transform 0.1s ease;
}

/* Secondary Button */
button.secondary,
.nav-button.secondary,
.btn-secondary {
    background: var(--btn-secondary);
    color: var(--text-primary);
    border-color: var(--border-light);
}

button.secondary:hover,
.nav-button.secondary:hover,
.btn-secondary:hover {
    background: var(--btn-secondary-hover);
    border-color: var(--border-medium);
}

/* Success Button */
.btn-success {
    background: var(--btn-success);
    color: var(--text-white);
    border-color: var(--btn-success);
}

.btn-success:hover {
    background: var(--btn-success-hover);
    border-color: var(--btn-success-hover);
    transform: translateY(-1px);
}

/* Danger Button */
.btn-danger {
    background: var(--btn-danger);
    color: var(--text-white);
    border-color: var(--btn-danger);
}

.btn-danger:hover {
    background: var(--btn-danger-hover);
    border-color: var(--btn-danger-hover);
    transform: translateY(-1px);
}

/* Small Button */
.btn-sm {
    padding: var(--space-2) var(--space-3);
    font-size: 0.75rem;
    min-height: 32px;
}

/* Large Button */
.btn-lg {
    padding: var(--space-4) var(--space-6);
    font-size: 1rem;
    min-height: 48px;
}

/* === Dashboard Header === */
.home-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--space-6) 0;
    border-bottom: 1px solid var(--border-light);
    margin-bottom: var(--space-8);
}

.home-header h1 {
    font-size: 1.5rem;
    font-weight: var(--font-weight-medium);
    color: var(--text-primary);
    text-align: left;
}

/* Logout Button */
#logoutButton {
    background: var(--btn-danger);
    color: white;
    border: 1px solid var(--btn-danger);
    border-radius: var(--radius-lg);
    padding: var(--space-2) var(--space-4);
    font-size: 0.875rem;
    font-weight: var(--font-weight-medium);
    cursor: pointer;
    transition: all var(--transition);
}

#logoutButton:hover {
    background: var(--btn-danger-hover);
    border-color: var(--btn-danger-hover);
    transform: translateY(-1px);
}

/* === Dashboard Header === */
.home-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--space-lg) 0;
    border-bottom: 1px solid var(--divider-color);
    margin-bottom: var(--space-xl);
    gap: var(--space-lg);
    flex-wrap: wrap;
}

.home-header h1 {
    margin: 0;
    font-size: 1.75rem;
    font-weight: var(--font-weight-medium);
    color: var(--primary-text);
    display: flex;
    align-items: center;
    gap: var(--space-sm);
}

.home-header h1::before {
    content: "🏫";
    font-size: 1.5rem;
}

/* Logout Button */
#logoutButton {
    background-color: var(--danger-action);
    color: white;
    border: 1px solid var(--danger-action);
    border-radius: var(--radius-sm);
    padding: var(--space-sm) var(--space-lg);
    font-size: 0.875rem;
    font-weight: var(--font-weight-medium);
    cursor: pointer;
    transition: all var(--transition-fast);
    box-shadow: var(--shadow-sm);
    min-height: 40px;
}

#logoutButton:hover {
    background-color: var(--danger-action-hover);
    border-color: var(--danger-action-hover);
    box-shadow: var(--shadow-md);
    transform: translateY(-1px);
}

/* === Main Content === */
.home-main {
    background: var(--secondary-bg);
    border-radius: var(--radius-xl);
    padding: var(--space-8);
    box-shadow: var(--shadow-1);
    border: 1px solid var(--border-light);
}

.home-main h2 {
    font-size: 1.25rem;
    font-weight: var(--font-weight-medium);
    color: var(--text-primary);
    margin-bottom: var(--space-6);
}

/* === Enhanced Search Design === */
.search-container {
    margin-bottom: var(--space-6);
    display: flex;
    justify-content: center;
    position: relative;
}

.search-container input {
    max-width: 480px;
    width: 100%;
    padding: var(--space-4) var(--space-5);
    border-radius: var(--radius-lg);
    border: 2px solid var(--border-light);
    background: var(--secondary-bg);
    font-size: 0.95rem;
    font-family: var(--font-primary);
    color: var(--text-primary);
    transition: all var(--transition);
    box-shadow: var(--shadow-sm);
    font-weight: var(--font-weight-normal);
}

.search-container input:focus {
    outline: none;
    border-color: var(--accent-yellow);
    box-shadow: 0 0 0 3px rgba(251, 191, 36, 0.15), var(--shadow-md);
    background: var(--secondary-bg);
}

.search-container input::placeholder {
    color: var(--text-muted);
    font-weight: var(--font-weight-normal);
}

/* Detail View Search Container */
.detail-card .search-container {
    margin-bottom: var(--space-5);
    padding: 0 var(--space-2);
}

.detail-card .search-container input {
    max-width: 100%;
    padding: var(--space-3) var(--space-4);
    font-size: 0.875rem;
    border-radius: var(--radius-md);
}

/* Modal Search Container */
.modal-content .search-container {
    margin-bottom: var(--space-5);
    display: flex;
    justify-content: center;
    padding: 0 var(--space-4);
}

.modal-content .search-container input {
    max-width: 400px;
    width: 100%;
    padding: var(--space-3) var(--space-4);
    border-radius: var(--radius-md);
    border: 2px solid var(--border-light);
    background: var(--secondary-bg);
    font-size: 0.875rem;
    font-family: var(--font-primary);
    color: var(--text-primary);
    transition: all var(--transition);
    box-shadow: var(--shadow-sm);
}

.modal-content .search-container input:focus {
    outline: none;
    border-color: var(--accent-yellow);
    box-shadow: 0 0 0 3px rgba(251, 191, 36, 0.15), var(--shadow-md);
}

/* === Professional Data Table === */
.data-table-container,
.table-container {
    background: var(--secondary-bg);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--border-light);
    overflow: hidden;
    width: 100%;
    box-sizing: border-box;
}

.data-table-header {
    padding: var(--space-5) var(--space-6);
    border-bottom: 1px solid var(--border-light);
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: var(--secondary-bg);
}

.data-table-title {
    font-size: 1.125rem;
    font-weight: var(--font-weight-semibold);
    color: var(--text-primary);
    margin: 0;
}

.data-table-actions {
    display: flex;
    gap: var(--space-3);
    align-items: center;
}

.data-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 0.875rem;
    font-family: var(--font-primary);
}

.data-table th {
    background: var(--primary-bg);
    padding: var(--space-4) var(--space-6);
    text-align: left;
    font-weight: var(--font-weight-semibold);
    color: var(--text-secondary);
    border-bottom: 1px solid var(--border-light);
    font-size: 0.75rem;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    vertical-align: middle;
    font-family: var(--font-primary);
}

.data-table td {
    padding: var(--space-4) var(--space-6);
    border-bottom: 1px solid var(--border-light);
    color: var(--text-primary);
    vertical-align: middle;
    font-size: 0.875rem;
    font-family: var(--font-primary);
    line-height: 1.5;
}

.data-table tbody tr {
    transition: all var(--transition);
    cursor: pointer;
}

.data-table tbody tr:hover {
    background: var(--primary-bg);
}

.data-table tbody tr:last-child td {
    border-bottom: none;
}

/* Sortable Table Headers */
.data-table th.sortable {
    cursor: pointer;
    user-select: none;
    position: relative;
    transition: all var(--transition);
}

.data-table th.sortable:hover {
    background-color: var(--accent-yellow-light);
    color: var(--text-primary);
}

.sort-indicator {
    margin-left: var(--space-2);
    font-size: 0.75rem;
    opacity: 0.5;
    transition: all var(--transition);
}

.data-table th.sortable.sort-asc .sort-indicator::after {
    content: '▲';
    opacity: 1;
    color: var(--accent-yellow);
}

.data-table th.sortable.sort-desc .sort-indicator::after {
    content: '▼';
    opacity: 1;
    color: var(--accent-yellow);
}

.data-table th.sortable:not(.sort-asc):not(.sort-desc) .sort-indicator::after {
    content: '⇅';
}

.data-table th.sortable.sort-asc,
.data-table th.sortable.sort-desc {
    background-color: var(--accent-yellow-light);
    color: var(--text-primary);
}

/* Table Cell Content */
.table-cell-primary {
    font-weight: var(--font-weight-medium);
    color: var(--text-primary);
}

.table-cell-secondary {
    color: var(--text-secondary);
    font-size: 0.8125rem;
}

.table-cell-badge {
    display: inline-flex;
    align-items: center;
    padding: var(--space-1) var(--space-3);
    border-radius: var(--radius-sm);
    font-size: 0.75rem;
    font-weight: var(--font-weight-medium);
    text-transform: uppercase;
    letter-spacing: 0.025em;
}

.table-cell-badge.active {
    background: rgba(16, 185, 129, 0.1);
    color: var(--btn-success);
}

.table-cell-badge.inactive {
    background: rgba(239, 68, 68, 0.1);
    color: var(--btn-danger);
}

.table-actions {
    display: flex;
    gap: var(--space-2);
    align-items: center;
}

.table-action-btn {
    padding: var(--space-2);
    border: none;
    background: none;
    color: var(--text-muted);
    cursor: pointer;
    border-radius: var(--radius-sm);
    transition: all var(--transition);
    display: flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;
}

.table-action-btn:hover {
    background: var(--primary-bg);
    color: var(--text-primary);
}

.table-action-btn.edit:hover {
    background: rgba(59, 130, 246, 0.1);
    color: var(--text-link);
}

.table-action-btn.delete:hover {
    background: rgba(239, 68, 68, 0.1);
    color: var(--btn-danger);
}

/* === Content Views === */
.content-view {
    display: none;
}

.content-view.active {
    display: block;
}

/* === Teacher Detail View === */
.teacher-detail-container {
    background: transparent;
    padding: var(--space-4);
    height: calc(100vh - 200px);
    display: flex;
    flex-direction: column;
}



/* Desktop: Show both sections */
.detail-section.desktop-visible {
    display: block !important;
}

/* Mobile: Hide sections by default, show active */
.detail-section {
    display: none;
}

.detail-section.active {
    display: block !important;
}

/* Students section should take remaining space */
.detail-section[data-content="students"] {
    flex: 1;
    display: flex;
    flex-direction: column;
}

.detail-section[data-content="students"].desktop-visible {
    flex: 1;
    display: flex !important;
    flex-direction: column;
}

.detail-section[data-content="students"] .detail-card {
    flex: 1;
    display: flex !important;
    flex-direction: column;
    min-height: 400px;
}

.detail-card {
    background: var(--secondary-bg);
    border-radius: var(--radius-lg);
    padding: var(--space-4);
    margin-bottom: var(--space-4);
    border: 1px solid var(--border-light);
    box-shadow: var(--shadow-sm);
}

.detail-card h2 {
    font-size: 1rem;
    font-weight: var(--font-weight-semibold);
    color: var(--text-primary);
    margin: 0 0 var(--space-4) 0;
    padding-bottom: var(--space-3);
    border-bottom: 1px solid var(--border-light);
}

/* Compact Teacher Information Section */
.detail-card.teacher-info-compact {
    padding: var(--space-3);
    margin-bottom: var(--space-3);
    max-width: 100%;
    width: 100%;
    position: relative;
}

.detail-card.teacher-info-compact h2 {
    font-size: 0.9rem;
    margin: 0 0 var(--space-2) 0;
    padding-bottom: var(--space-2);
    display: flex;
    align-items: center;
    justify-content: space-between;
    cursor: pointer;
    user-select: none;
}

.teacher-info-toggle {
    background: var(--accent-yellow);
    color: var(--text-primary);
    border: none;
    border-radius: var(--radius-sm);
    padding: var(--space-1) var(--space-2);
    font-size: 0.75rem;
    font-weight: var(--font-weight-medium);
    cursor: pointer;
    transition: all var(--transition);
    min-width: 60px;
    text-align: center;
}

.teacher-info-toggle:hover {
    background: var(--accent-yellow-dark);
    transform: translateY(-1px);
}

.teacher-info-content {
    transition: all var(--transition);
    overflow: hidden;
}

.teacher-info-content.collapsed {
    max-height: 0;
    opacity: 0;
    margin: 0;
    padding: 0;
}

.teacher-info-content.expanded {
    max-height: 500px;
    opacity: 1;
    margin-top: var(--space-2);
}

.detail-info-grid {
    display: grid;
    gap: var(--space-3);
}

.detail-info-grid p {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--space-3);
    background: var(--primary-bg);
    border-radius: var(--radius-md);
    border: 1px solid var(--border-light);
    margin: 0;
    font-size: 0.8125rem;
}

.detail-info-grid p strong {
    color: var(--text-primary);
    font-weight: var(--font-weight-medium);
    min-width: 100px;
    font-size: 0.8125rem;
}

/* Compact Teacher Info Grid */
.teacher-info-compact .detail-info-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--space-2);
    width: 100%;
}

.teacher-info-compact .detail-info-grid p {
    padding: var(--space-2) var(--space-3);
    font-size: 0.8rem;
    border-radius: var(--radius-sm);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.teacher-info-compact .detail-info-grid p strong {
    min-width: 80px;
    font-size: 0.8rem;
    flex-shrink: 0;
}

/* Password field styling in compact view */
.teacher-info-compact .password-container {
    display: flex;
    align-items: center;
    gap: var(--space-2);
    flex: 1;
    justify-content: flex-end;
    min-width: 0;
}

.teacher-info-compact .password-value,
.teacher-info-compact .password-dots {
    font-family: monospace;
    font-size: 0.75rem;
    min-width: 60px;
    text-align: right;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.teacher-info-compact .password-toggle-btn {
    background: var(--btn-secondary);
    border: 1px solid var(--border-light);
    border-radius: var(--radius-sm);
    padding: var(--space-1) var(--space-2);
    font-size: 0.7rem;
    cursor: pointer;
    transition: all var(--transition);
    min-width: 45px;
    flex-shrink: 0;
}

.teacher-info-compact .password-toggle-btn:hover {
    background: var(--btn-secondary-hover);
    border-color: var(--border-medium);
}

.detail-student-list {
    flex: 1;
    overflow-y: auto;
    overflow-x: hidden;
    background: transparent;
    padding: 0;
    min-height: 400px;
    width: 100%;
    min-width: 0;
    box-sizing: border-box;
    position: relative;
    display: grid !important;
    grid-template-columns: 1fr;
    gap: var(--space-3);
    align-content: start;
    visibility: visible !important;
}

/* Responsive grid layout for wider screens */
@media (min-width: 900px) and (max-width: 1199px) {
    .detail-student-list {
        grid-template-columns: 1fr 1fr;
        gap: var(--space-3);
    }
}

@media (min-width: 1200px) and (max-width: 1599px) {
    .detail-student-list {
        grid-template-columns: 1fr 1fr;
        gap: var(--space-4);
    }
}

@media (min-width: 1600px) {
    .detail-student-list {
        grid-template-columns: 1fr 1fr 1fr;
        gap: var(--space-4);
    }
}

/* Enhanced No Results and Loading Messages */
.detail-student-list > p:only-child,
.modal-student-list > p:only-child,
.detail-student-list > .loading-text,
.modal-student-list > .loading-text {
    text-align: center;
    padding: var(--space-8) var(--space-6);
    color: var(--text-muted);
    font-size: 0.95rem;
    font-weight: var(--font-weight-medium);
    background: var(--secondary-bg);
    border-radius: var(--radius-lg);
    border: 2px dashed var(--border-light);
    margin: var(--space-4) 0;
    position: relative;
}

.detail-student-list > p:only-child::before,
.modal-student-list > p:only-child::before {
    content: "📝";
    display: block;
    font-size: 2rem;
    margin-bottom: var(--space-3);
    opacity: 0.6;
}

/* Loading text specific styling */
.detail-student-list .loading-text,
.modal-student-list .loading-text {
    background: linear-gradient(90deg, var(--text-muted) 25%, var(--accent-yellow) 50%, var(--text-muted) 75%);
    background-size: 200% 100%;
    animation: shimmer 2s infinite;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    border: 2px dashed var(--accent-yellow-light);
}

.detail-student-list .loading-text::before,
.modal-student-list .loading-text::before {
    content: "⏳";
    display: block;
    font-size: 2rem;
    margin-bottom: var(--space-3);
    opacity: 0.8;
    animation: pulse 1.5s infinite;
}

@keyframes shimmer {
    0% { background-position: -200% 0; }
    100% { background-position: 200% 0; }
}

@keyframes pulse {
    0%, 100% { opacity: 0.8; }
    50% { opacity: 0.4; }
}

/* Student items in detail view */
.detail-student-list .student-item {
    background: var(--secondary-bg);
    padding: var(--space-3);
    border-radius: var(--radius-md);
    border: 1px solid var(--border-light);
    overflow: hidden;
    word-wrap: break-word;
    min-width: 0;
    width: 100%;
    box-sizing: border-box;
    position: relative;
    display: flex;
    flex-direction: column;
    align-self: start;
    height: fit-content;
}

/* Student Details Modal */
.student-details-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(4px);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    transition: all var(--transition);
    box-sizing: border-box;
    padding: var(--space-4);
}

.student-details-modal.active {
    opacity: 1;
    visibility: visible;
}

.student-modal-content {
    background: var(--secondary-bg);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-3);
    width: 95%;
    max-width: 900px;
    max-height: 90vh;
    overflow-y: auto;
    position: relative;
    transform: scale(0.95);
    transition: transform var(--transition);
    display: flex;
    flex-direction: column;
    padding: var(--space-8);
    box-sizing: border-box;
    margin: auto;
}

.student-details-modal.active .student-modal-content {
    transform: scale(1);
}

.student-modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--space-4);
    padding-bottom: var(--space-3);
    border-bottom: 1px solid var(--border-light);
}

.student-modal-header h3 {
    margin: 0;
    color: var(--text-primary);
    font-size: 1.25rem;
    font-weight: var(--font-weight-semibold);
    padding-right: 80px;
    box-sizing: border-box;
    word-wrap: break-word;
}

.student-modal-close {
    position: absolute;
    top: var(--space-4);
    right: var(--space-4);
    z-index: 20;
    background: var(--btn-danger);
    color: white;
    border: none;
    border-radius: var(--radius-md);
    padding: var(--space-1) var(--space-3);
    font-size: 0.75rem;
    font-weight: var(--font-weight-medium);
    cursor: pointer;
    transition: all var(--transition);
    min-height: 28px;
    min-width: 50px;
    max-width: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
    box-sizing: border-box;
}

.student-modal-close:hover {
    background: var(--btn-danger-hover);
    transform: translateY(-1px);
}

/* Student Edit Button */
.student-edit-btn {
    position: absolute;
    top: var(--space-4);
    right: 80px;
    z-index: 20;
    background: var(--accent-yellow);
    color: var(--text-primary);
    border: none;
    border-radius: var(--radius-md);
    padding: var(--space-1) var(--space-3);
    font-size: 0.75rem;
    font-weight: var(--font-weight-medium);
    cursor: pointer;
    transition: all var(--transition);
    min-height: 28px;
    min-width: 50px;
    max-width: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
    box-sizing: border-box;
}

.student-edit-btn:hover {
    background: var(--accent-yellow-dark);
    transform: translateY(-1px);
}

.student-modal-body {
    display: flex;
    flex-direction: column;
    gap: var(--space-3);
}

.student-modal-body p {
    margin: 0;
    font-size: 0.875rem;
    line-height: 1.5;
    color: var(--text-secondary);
}

.student-modal-body p strong {
    color: var(--text-primary);
    font-weight: var(--font-weight-medium);
}

/* Student Info Grid Layout - 2 columns horizontal */
.student-info-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--space-4) var(--space-6);
    margin-bottom: var(--space-6);
    padding: var(--space-4);
    background: rgba(255, 255, 255, 0.5);
    border-radius: var(--radius-md);
    border: 1px solid rgba(243, 156, 18, 0.2);
}

.info-item {
    display: flex;
    align-items: center;
    gap: var(--space-2);
    min-height: 32px;
}

.info-label {
    font-weight: var(--font-weight-semibold);
    color: var(--text-primary);
    font-size: 0.9rem;
    white-space: nowrap;
    min-width: fit-content;
}

.info-value {
    color: var(--text-secondary);
    font-size: 0.9rem;
    word-break: break-word;
    flex: 1;
}

.password-container {
    display: inline-flex;
    align-items: center;
    gap: var(--space-2);
}

.password-toggle-btn {
    padding: 4px 8px;
    background: rgba(243, 156, 18, 0.1);
    border: 1px solid rgba(243, 156, 18, 0.3);
    border-radius: var(--radius-sm);
    cursor: pointer;
    font-size: 0.75rem;
    min-width: 45px;
    color: #e67e22;
    transition: all 0.2s ease;
}

.password-toggle-btn:hover {
    background: rgba(243, 156, 18, 0.2);
    border-color: rgba(243, 156, 18, 0.5);
}

/* Responsive adjustments for student info grid */
@media (max-width: 600px) {
    .student-info-grid {
        grid-template-columns: 1fr;
        gap: var(--space-3);
    }

    .info-item {
        flex-direction: column;
        align-items: flex-start;
        gap: var(--space-1);
    }
}

.student-modal-body .level-list {
    padding-left: var(--space-4);
    margin: var(--space-2) 0;
}

/* Progress Section Styling */
.progress-section {
    background: linear-gradient(135deg, rgba(243, 156, 18, 0.05), rgba(230, 126, 34, 0.05));
    border-radius: var(--radius-md);
    padding: var(--space-4);
    margin: var(--space-4) 0;
    border: 1px solid rgba(243, 156, 18, 0.2);
}

.progress-header h4 {
    margin: 0 0 var(--space-3) 0;
    color: var(--text-primary);
    font-size: 1.1rem;
    font-weight: var(--font-weight-semibold);
}

.progress-stats {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: var(--space-4);
    margin-bottom: var(--space-4);
}

.stat-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: var(--space-2);
    background: rgba(255, 255, 255, 0.7);
    border-radius: var(--radius-sm);
    border: 1px solid rgba(243, 156, 18, 0.1);
}

.stat-label {
    font-size: 0.75rem;
    color: var(--text-secondary);
    margin-bottom: var(--space-1);
    text-align: center;
}

.stat-value {
    font-size: 1.1rem;
    font-weight: var(--font-weight-semibold);
    color: #e67e22;
}

.progress-bar-container {
    margin-top: var(--space-3);
    padding: var(--space-3);
    background: rgba(255, 255, 255, 0.7);
    border-radius: var(--radius-md);
    border: 1px solid rgba(243, 156, 18, 0.3);
    box-shadow: 0 2px 4px rgba(243, 156, 18, 0.1);
}

/* Progress Display for Teacher Portal Table */
.progress-display {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--space-2);
    min-width: 80px;
}

.progress-percentage {
    font-size: 0.875rem;
    font-weight: var(--font-weight-semibold);
    color: var(--text-primary);
    font-family: var(--font-primary);
    line-height: 1;
}

.progress-bar {
    width: 100%;
    height: 8px;
    background: var(--border-light);
    border-radius: 4px;
    overflow: hidden;
    position: relative;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, var(--accent-yellow), var(--accent-yellow-dark));
    border-radius: 4px;
    transition: width 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    min-width: 2px;
    position: relative;
    overflow: hidden;
}

.progress-fill::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    animation: progressShine 2s infinite;
}

@keyframes progressShine {
    0% { left: -100%; }
    100% { left: 100%; }
}

.progress-bar span {
    font-size: 0.8rem;
    color: #666;
}

.progress-text {
    font-size: 0.85rem;
    color: var(--text-primary);
    text-align: center;
    display: block;
    margin: 0;
    padding: 0;
    font-weight: var(--font-weight-medium);
    line-height: 1.2;
}

/* Level Details Section */
.level-details {
    margin-top: var(--space-4);
}

.level-details h4 {
    margin: 0 0 var(--space-3) 0;
    color: var(--text-primary);
    font-size: 1.1rem;
    font-weight: var(--font-weight-semibold);
}

.level-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(160px, 1fr));
    gap: var(--space-3);
}

.level-card {
    background: rgba(255, 255, 255, 0.8);
    border-radius: var(--radius-sm);
    padding: var(--space-3);
    border: 1px solid rgba(243, 156, 18, 0.2);
    text-align: center;
    transition: all 0.2s ease;
}

.level-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(243, 156, 18, 0.2);
}

.level-card.completed {
    background: linear-gradient(135deg, rgba(46, 204, 113, 0.1), rgba(39, 174, 96, 0.1));
    border-color: rgba(46, 204, 113, 0.3);
}

.level-card.incomplete {
    background: linear-gradient(135deg, rgba(231, 76, 60, 0.1), rgba(192, 57, 43, 0.1));
    border-color: rgba(231, 76, 60, 0.3);
}

.level-number {
    font-size: 0.9rem;
    font-weight: var(--font-weight-semibold);
    color: var(--text-primary);
    margin-bottom: var(--space-1);
}

.level-score {
    font-size: 0.8rem;
    color: var(--text-secondary);
    margin-bottom: var(--space-1);
}

.level-status {
    font-size: 0.75rem;
    font-weight: var(--font-weight-medium);
}

.level-card.completed .level-status {
    color: #27ae60;
}

.level-card.incomplete .level-status {
    color: #e74c3c;
}

.detail-student-list .student-summary {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: var(--space-2);
    min-width: 0;
    flex-wrap: wrap;
}

.detail-student-list .student-summary h4 {
    font-size: 0.9rem;
    margin: 0;
    color: var(--text-primary);
    font-weight: var(--font-weight-medium);
    flex: 1;
    min-width: 0;
    word-wrap: break-word;
    overflow-wrap: break-word;
}

.detail-student-list .student-toggle-button {
    font-size: 0.75rem;
    padding: var(--space-1) var(--space-2);
    background: var(--btn-secondary);
    border: 1px solid var(--border-light);
    border-radius: var(--radius-sm);
    cursor: pointer;
    flex-shrink: 0;
    white-space: nowrap;
}

/* Student details section */
.detail-student-list .student-details {
    width: 100%;
    min-width: 0;
    overflow: hidden;
    word-wrap: break-word;
    overflow-wrap: break-word;
    box-sizing: border-box;
    margin-top: var(--space-2);
    padding: var(--space-3);
    background: var(--secondary-bg);
    border-radius: var(--radius-md);
    display: flex;
    flex-direction: column;
    gap: var(--space-2);
}

.detail-student-list .student-details p {
    font-size: 0.75rem;
    margin: 0;
    color: var(--text-secondary);
    word-wrap: break-word;
    overflow-wrap: break-word;
    width: 100%;
    min-width: 0;
    box-sizing: border-box;
    line-height: 1.4;
    hyphens: auto;
}

.detail-student-list .student-details p strong {
    color: var(--text-primary);
    font-weight: var(--font-weight-medium);
    font-size: 0.75rem;
    word-wrap: break-word;
    overflow-wrap: break-word;
}

/* Level list styling to prevent overflow */
.detail-student-list .level-list {
    width: 100%;
    min-width: 0;
    overflow: hidden;
    word-wrap: break-word;
    overflow-wrap: break-word;
    box-sizing: border-box;
    padding-left: var(--space-3);
    margin: 0;
    list-style-position: inside;
    display: flex;
    flex-direction: column;
    gap: 2px;
}

.detail-student-list .level-list li {
    font-size: 0.75rem;
    word-wrap: break-word;
    overflow-wrap: break-word;
    width: 100%;
    min-width: 0;
    box-sizing: border-box;
    white-space: normal;
    line-height: 1.3;
    hyphens: auto;
    display: flex;
    align-items: center;
    gap: var(--space-1);
}

.detail-student-list .level-info-container {
    width: 100%;
    min-width: 0;
    overflow: hidden;
    word-wrap: break-word;
    overflow-wrap: break-word;
    box-sizing: border-box;
    margin-top: var(--space-1);
}

/* === Settings Modal - Completely Isolated === */
.settings-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(4px);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.3s ease-out, visibility 0.3s ease-out;
}

.settings-overlay.active {
    opacity: 1;
    visibility: visible;
}

.settings-container {
    background: var(--secondary-bg);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-3);
    width: 90%;
    max-width: 500px;
    max-height: 600px;
    padding: var(--space-8);
    position: relative;
    box-sizing: border-box;
}

.settings-close-btn {
    position: absolute;
    top: var(--space-4);
    right: var(--space-4);
    background: var(--btn-danger);
    color: white;
    border: none;
    border-radius: var(--radius-md);
    padding: var(--space-1) var(--space-3);
    font-size: 0.75rem;
    font-weight: var(--font-weight-medium);
    cursor: pointer;
    transition: background 0.2s ease-out;
    min-height: 28px;
    min-width: 50px;
    max-width: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
    box-sizing: border-box;
}

.settings-close-btn:hover {
    background: var(--btn-danger-hover);
}

.settings-container h2 {
    color: var(--text-primary);
    margin: 0 0 var(--space-6) 0;
    font-size: 1.375rem;
    font-weight: var(--font-weight-medium);
    padding-bottom: var(--space-6);
    padding-right: 80px;
    border-bottom: 1px solid var(--border-light);
    line-height: 1.3;
    box-sizing: border-box;
}

/* === Add Student Modal - Completely Isolated === */
.add-student-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.6);
    backdrop-filter: blur(4px);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 2000;
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.3s ease-out, visibility 0.3s ease-out;
}

.add-student-overlay.active {
    opacity: 1;
    visibility: visible;
}

.add-student-container {
    background: var(--secondary-bg);
    border-radius: var(--radius-xl);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
    width: 95%;
    max-width: 600px;
    min-width: 400px;
    padding: var(--space-8);
    position: relative;
    text-align: center;
    transform: scale(0.9) translateY(20px);
    transition: transform 0.3s ease-out;
}

.add-student-overlay.active .add-student-container {
    transform: scale(1) translateY(0);
}

.add-student-close-btn {
    position: absolute;
    top: var(--space-4);
    right: var(--space-4);
    background: var(--btn-danger);
    color: white;
    border: none;
    border-radius: var(--radius-md);
    padding: var(--space-1) var(--space-3);
    font-size: 0.75rem;
    font-weight: var(--font-weight-medium);
    cursor: pointer;
    transition: background 0.2s ease-out, transform 0.2s ease-out;
}

.add-student-close-btn:hover {
    background: var(--btn-danger-hover);
    transform: translateY(-1px);
}

.add-student-container h2 {
    color: var(--text-primary);
    margin: 0 0 var(--space-6) 0;
    font-size: 1.375rem;
    font-weight: var(--font-weight-medium);
    padding-right: 80px;
}

.add-student-form {
    display: flex;
    flex-direction: column;
    gap: var(--space-5);
    align-items: center;
    width: 100%;
}

.input-wrapper {
    width: 100%;
    max-width: 400px;
}

.input-wrapper input {
    width: 100%;
    padding: var(--space-4);
    border: 2px solid var(--border-light);
    border-radius: var(--radius-md);
    font-size: 1.125rem;
    text-align: center;
    background: var(--input-bg);
    color: var(--text-primary);
    box-sizing: border-box;
    transition: border-color 0.2s ease-out, box-shadow 0.2s ease-out;
    min-height: 50px;
}

.input-wrapper input:focus {
    outline: none;
    border-color: var(--primary);
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.create-btn {
    background: #3b82f6;
    color: #ffffff !important;
    border: none;
    border-radius: var(--radius-md);
    padding: var(--space-4) var(--space-8);
    font-size: 1.125rem;
    font-weight: var(--font-weight-medium);
    cursor: pointer;
    transition: background 0.2s ease-out, transform 0.2s ease-out, box-shadow 0.2s ease-out;
    min-width: 150px;
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
    margin-top: var(--space-2);
}

.create-btn:hover {
    background: #2563eb;
    color: #ffffff !important;
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(59, 130, 246, 0.4);
}

.create-btn:active {
    transform: translateY(0);
    box-shadow: 0 2px 8px rgba(59, 130, 246, 0.3);
}

.add-student-error {
    color: var(--btn-danger);
    font-size: 0.875rem;
    font-weight: var(--font-weight-medium);
    text-align: center;
    margin-top: var(--space-2);
}

/* Pagination Styles */
.pagination-container {
    display: flex;
    justify-content: center;
    margin-top: var(--space-6);
    margin-bottom: var(--space-4);
}

.pagination {
    display: flex;
    gap: var(--space-2);
    align-items: center;
}

.pagination-btn {
    background: #f8fafc;
    color: #374151;
    border: 2px solid #d1d5db;
    border-radius: var(--radius-md);
    padding: var(--space-2) var(--space-3);
    font-size: 0.875rem;
    font-weight: var(--font-weight-medium);
    cursor: pointer;
    transition: all 0.2s ease-out;
    min-width: 40px;
    text-align: center;
}

.pagination-btn:hover {
    background: #3b82f6;
    color: #ffffff !important;
    border-color: #3b82f6;
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(59, 130, 246, 0.2);
}

.pagination-btn.active {
    background: #3b82f6 !important;
    color: #ffffff !important;
    border-color: #3b82f6 !important;
    box-shadow: 0 2px 8px rgba(59, 130, 246, 0.3);
}

.pagination-btn:active {
    transform: translateY(0);
}

/* === Progress Modal - Completely Isolated === */
.progress-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.7);
    backdrop-filter: blur(6px);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 2100;
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.3s ease-out, visibility 0.3s ease-out;
}

.progress-overlay.active {
    opacity: 1;
    visibility: visible;
}

.progress-container {
    background: var(--secondary-bg);
    border-radius: var(--radius-xl);
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.2);
    width: 90%;
    max-width: 600px;
    padding: var(--space-8);
    text-align: center;
    transform: scale(0.9) translateY(30px);
    transition: transform 0.3s ease-out;
}

.progress-overlay.active .progress-container {
    transform: scale(1) translateY(0);
}

.progress-container h2 {
    color: var(--text-primary);
    margin: 0 0 var(--space-2) 0;
    font-size: 1.5rem;
    font-weight: var(--font-weight-medium);
}

.progress-container p {
    color: var(--text-secondary);
    margin: 0 0 var(--space-6) 0;
    font-size: 0.875rem;
}

.progress-wrapper {
    display: flex;
    flex-direction: column;
    gap: var(--space-4);
    align-items: center;
}

.progress-status {
    font-size: 1.125rem;
    font-weight: var(--font-weight-medium);
    color: var(--text-primary);
    padding: var(--space-2);
    background: var(--primary-bg);
    border-radius: var(--radius-md);
    border: 1px solid var(--border-light);
}

.progress-track {
    width: 100%;
    height: 12px;
    background: var(--border-light);
    border-radius: var(--radius-full);
    overflow: hidden;
    box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.1);
}

.progress-bar-fill {
    height: 100%;
    background: linear-gradient(90deg, var(--primary), var(--primary-hover));
    border-radius: var(--radius-full);
    transition: width 0.4s ease-out;
    width: 0%;
    box-shadow: 0 2px 4px rgba(59, 130, 246, 0.3);
}

.cancel-btn {
    background: var(--btn-danger);
    color: white;
    border: none;
    border-radius: var(--radius-md);
    padding: var(--space-3) var(--space-6);
    font-size: 0.875rem;
    font-weight: var(--font-weight-medium);
    cursor: pointer;
    transition: background 0.2s ease-out, transform 0.2s ease-out;
    margin-top: var(--space-2);
}

.cancel-btn:hover {
    background: var(--btn-danger-hover);
    transform: translateY(-1px);
}

/* Student Edit Modal Specific Styling */
.student-edit-modal .modal-content {
    width: 90%;
    max-width: 480px;
    max-height: 85vh;
    margin: auto;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    padding: var(--space-8);
    background: var(--secondary-bg);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-xl);
    border: 1px solid var(--border-light);
    transform: scale(0.95);
    transition: transform var(--transition);
}

.student-edit-modal.active .modal-content {
    transform: scale(1);
}

/* Student Edit Form Styling */
.student-edit-modal .settings-content {
    width: 100%;
    max-width: 400px;
    margin: 0 auto;
}

.student-edit-modal .create-account-form {
    display: flex;
    flex-direction: column;
    gap: var(--space-5);
    width: 100%;
}

.student-edit-modal .form-group {
    display: flex;
    flex-direction: column;
    gap: var(--space-2);
    width: 100%;
}

.student-edit-modal .form-group label {
    font-size: 0.875rem;
    font-weight: var(--font-weight-medium);
    color: var(--text-primary);
    text-align: left;
}

.student-edit-modal .form-group input {
    width: 100%;
    padding: var(--space-3) var(--space-4);
    border: 1px solid var(--border-light);
    border-radius: var(--radius-md);
    background: var(--secondary-bg);
    font-size: 0.875rem;
    color: var(--text-primary);
    transition: all var(--transition);
    box-sizing: border-box;
}

.student-edit-modal .form-group input:focus {
    outline: none;
    border-color: var(--accent-yellow);
    box-shadow: 0 0 0 3px rgba(251, 191, 36, 0.15);
}

.student-edit-modal .form-actions {
    display: flex;
    gap: var(--space-3);
    justify-content: center;
    margin-top: var(--space-4);
    width: 100%;
}

.student-edit-modal .form-actions button {
    flex: 1;
    max-width: 120px;
    padding: var(--space-3) var(--space-4);
    font-size: 0.875rem;
    font-weight: var(--font-weight-medium);
    border-radius: var(--radius-md);
    cursor: pointer;
    transition: all var(--transition);
    min-height: 40px;
}

.student-edit-modal h2 {
    text-align: center;
    margin-bottom: var(--space-6);
    color: var(--text-primary);
    font-size: 1.25rem;
    font-weight: var(--font-weight-semibold);
}

.settings-content {
    display: flex;
    flex-direction: column;
    gap: var(--space-6);
}

.settings-section {
    border-bottom: 1px solid var(--border-light);
    padding-bottom: var(--space-6);
}

.settings-section:last-child {
    border-bottom: none;
    padding-bottom: 0;
}

.settings-section h3 {
    font-size: 1rem;
    font-weight: var(--font-weight-semibold);
    color: var(--text-primary);
    margin: 0 0 var(--space-4) 0;
}

.settings-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: var(--space-4);
    padding: var(--space-4);
    border-radius: var(--radius-md);
    transition: all var(--transition);
}

.settings-item:hover {
    background: var(--primary-bg);
}

.settings-item-info {
    flex: 0 0 70%;
    width: 70%;
}

.settings-item-title {
    font-size: 0.875rem;
    font-weight: var(--font-weight-medium);
    color: var(--text-primary);
    margin-bottom: var(--space-1);
}

.settings-item-description {
    font-size: 0.75rem;
    color: var(--text-secondary);
    line-height: 1.4;
}

/* Settings Modal Close Button Specific Positioning */
.settings-modal .modal-close-btn {
    position: absolute;
    top: var(--space-4);
    right: var(--space-4);
    z-index: 20;
    background: var(--btn-danger);
    color: white;
    border: none;
    border-radius: var(--radius-md);
    padding: var(--space-1) var(--space-3);
    font-size: 0.75rem;
    font-weight: var(--font-weight-medium);
    cursor: pointer;
    transition: all var(--transition);
    min-height: 28px;
    min-width: 50px;
    max-width: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
    box-sizing: border-box;
}

.settings-modal .modal-close-btn:hover {
    background: var(--btn-danger-hover);
    transform: translateY(-1px);
}

/* Settings Modal Header Spacing */
.settings-modal h2 {
    padding-right: 80px;
    box-sizing: border-box;
    word-wrap: break-word;
}

/* Settings Modal Logout Button - High Priority */
.settings-modal #logoutButton,
.settings-modal #settingsLogoutButton {
    flex: 0 0 30%;
    width: 30%;
    padding: var(--space-3) var(--space-4) !important;
    font-size: 0.875rem !important;
    font-weight: var(--font-weight-semibold) !important;
    border-radius: var(--radius-md) !important;
    background: var(--btn-danger) !important;
    color: white !important;
    border: 1px solid var(--btn-danger) !important;
    cursor: pointer !important;
    transition: all var(--transition) !important;
    box-sizing: border-box;
    text-align: center;
    display: inline-block !important;
    visibility: visible !important;
    opacity: 1 !important;
}

.settings-modal #logoutButton:hover,
.settings-modal #settingsLogoutButton:hover {
    background: var(--btn-danger-hover) !important;
    border-color: var(--btn-danger-hover) !important;
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

/* Force logout button visibility - Override any conflicting styles */
.settings-modal .settings-option #logoutButton,
.settings-modal .settings-option button[id="logoutButton"],
.settings-modal button#logoutButton {
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
    background-color: #ef4444 !important;
    color: #ffffff !important;
    border: 1px solid #ef4444 !important;
    padding: 12px 16px !important;
    border-radius: 6px !important;
    font-size: 14px !important;
    font-weight: 600 !important;
    cursor: pointer !important;
    text-decoration: none !important;
    position: relative !important;
    z-index: 999 !important;
    width: auto !important;
    height: auto !important;
    min-height: 36px !important;
    line-height: normal !important;
}

/* Ensure logout button text is visible */
.settings-modal button#logoutButton:before,
.settings-modal button#logoutButton:after {
    content: none !important;
}

.settings-modal button#logoutButton span,
.settings-modal button#logoutButton {
    color: white !important;
    text-shadow: none !important;
}

/* Hover state for forced visibility */
.settings-modal .settings-option #logoutButton:hover,
.settings-modal .settings-option button[id="logoutButton"]:hover,
.settings-modal button#logoutButton:hover {
    background-color: #dc2626 !important;
    border-color: #dc2626 !important;
    color: #ffffff !important;
}

/* Ultimate logout button fix - nuclear option */
.settings-modal button,
.settings-modal .settings-option button,
.settings-modal [id*="logout"],
.settings-modal [id*="Logout"] {
    background: #ef4444 !important;
    color: #ffffff !important;
    border: 2px solid #ef4444 !important;
    padding: 10px 15px !important;
    border-radius: 5px !important;
    font-size: 14px !important;
    font-weight: bold !important;
    cursor: pointer !important;
    display: inline-block !important;
    visibility: visible !important;
    opacity: 1 !important;
    text-decoration: none !important;
    box-shadow: 0 2px 4px rgba(0,0,0,0.2) !important;
}

/* Force text to be visible */
.settings-modal button *,
.settings-modal .settings-option button *,
.settings-modal [id*="logout"] *,
.settings-modal [id*="Logout"] * {
    color: white !important;
}

/* === Create Account Form === */
.create-account-form-container {
    padding: var(--space-8);
}

.create-account-form {
    display: flex;
    flex-direction: column;
    gap: var(--space-6);
    max-width: 800px;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--space-6);
}

.form-actions {
    display: flex;
    gap: var(--space-4);
    justify-content: flex-end;
    margin-top: var(--space-4);
    padding-top: var(--space-6);
    border-top: 1px solid var(--border-light);
}

/* Mobile responsive form */
@media (max-width: 768px) {
    .form-row {
        grid-template-columns: 1fr;
        gap: var(--space-4);
    }

    .form-actions {
        flex-direction: column;
        align-items: stretch;
    }

    .create-account-form-container {
        padding: var(--space-4);
    }
}

/* === Legacy Teacher List (for compatibility) === */
.teacher-list.vertical-scroll {
    display: none; /* Hide old grid layout */
}

/* Teacher Cards */
.teacher-item-box {
    background: var(--card-bg);
    border: 1px solid var(--border-light);
    border-radius: var(--radius-xl);
    padding: var(--space-6);
    cursor: pointer;
    transition: all var(--transition-smooth);
    box-shadow: var(--shadow-1);
    display: flex;
    flex-direction: column;
    gap: var(--space-4);
    position: relative;
    overflow: hidden;
    opacity: 0;
    transform: translateY(20px);
}

.teacher-item-box::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, var(--accent-yellow), var(--accent-yellow-dark));
    transform: scaleX(0);
    transition: transform var(--transition-smooth);
}

.teacher-item-box::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(251, 191, 36, 0.1), transparent);
    transition: left var(--transition-slow);
}

.teacher-item-box:hover {
    transform: translateY(-4px) scale(1.02);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    border-color: var(--accent-yellow);
}

.teacher-item-box:hover::before {
    transform: scaleX(1);
}

.teacher-item-box:hover::after {
    left: 100%;
}
/* Teacher Card Content */
.teacher-item-box .teacher-name {
    font-size: 1.125rem;
    font-weight: var(--font-weight-semibold);
    color: var(--text-primary);
    margin: 0;
    line-height: 1.3;
}

.teacher-item-box .teacher-detail {
    font-size: 0.875rem;
    color: var(--text-secondary);
    line-height: 1.5;
    display: flex;
    align-items: flex-start;
    gap: var(--space-2);
    margin: 0;
}

.teacher-item-box .teacher-detail strong {
    color: var(--text-primary);
    font-weight: var(--font-weight-medium);
    min-width: 50px;
    flex-shrink: 0;
}

/* Loading and Error States */
.loading-text,
.teacher-list.vertical-scroll #loadingMessage,
.modal-info-grid .loading-text,
.modal-subcollection-list .loading-text,
.modal-student-list .loading-text,
.info-content-grid .loading-text,
.info-content-list .loading-text {
    padding: var(--space-xl);
    text-align: center;
    width: 100%;
    color: var(--muted-text);
    font-style: italic;
    font-size: 0.875rem;
    box-sizing: border-box;
}

.error-message {
    color: var(--danger-action);
    font-weight: var(--font-weight-medium);
    padding: var(--space-md);
    background-color: rgba(234, 67, 53, 0.1);
    border: 1px solid rgba(234, 67, 53, 0.3);
    border-radius: var(--radius-sm);
    margin: var(--space-md) 0;
    font-size: 0.875rem;
}


/* === Modal === */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(4px);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    transition: all var(--transition-smooth);
    box-sizing: border-box;
    padding: var(--space-4);
}

.modal-overlay.active {
    opacity: 1;
    visibility: visible;
    animation: fadeIn 0.3s var(--transition-smooth);
}

.modal-content {
    background: var(--secondary-bg);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-3);
    width: 95%;
    height: 95%;
    max-width: 1000px;
    max-height: 90vh;
    overflow-y: auto;
    position: relative;
    transform: scale(0.9) translateY(20px);
    transition: transform var(--transition-bounce);
    display: flex;
    flex-direction: column;
    padding: var(--space-8);
    box-sizing: border-box;
    margin: auto;
    opacity: 0;
}

.modal-overlay.active .modal-content {
    transform: scale(1) translateY(0);
    opacity: 1;
    animation: scaleIn 0.4s var(--transition-bounce) 0.1s both;
}

/* Modal Close Button */
.modal-close-btn {
    position: absolute;
    top: var(--space-6);
    right: var(--space-6);
    background: var(--btn-danger);
    color: white;
    border: none;
    border-radius: var(--radius-lg);
    padding: var(--space-2) var(--space-4);
    font-size: 0.875rem;
    font-weight: var(--font-weight-medium);
    cursor: pointer;
    transition: all var(--transition);
    min-height: 36px;
    min-width: 60px;
    z-index: 10;
    display: flex;
    align-items: center;
    justify-content: center;
    box-sizing: border-box;
}

.modal-close-btn:hover {
    background: var(--btn-danger-hover);
    transform: translateY(-1px);
}




/* Modal Headers */
.modal-content h2 {
    color: var(--text-primary);
    margin: 0 0 var(--space-6) 0;
    font-size: 1.375rem;
    font-weight: var(--font-weight-medium);
    padding-bottom: var(--space-6);
    padding-right: 100px;
    border-bottom: 1px solid var(--border-light);
    line-height: 1.3;
    box-sizing: border-box;
    word-wrap: break-word;
}

.modal-content h3 {
    color: var(--text-primary);
    margin: var(--space-8) 0 var(--space-6) 0;
    font-size: 1.125rem;
    font-weight: var(--font-weight-medium);
}

/* === Mobile Modal Navigation === */
.mobile-modal-nav {
    display: none;
    gap: var(--space-3);
    margin-bottom: var(--space-6);
    padding-bottom: var(--space-6);
    border-bottom: 1px solid var(--border-light);
}

.mobile-nav-btn {
    flex: 1;
    padding: var(--space-3) var(--space-5);
    background: var(--btn-secondary);
    border: 1px solid var(--border-light);
    border-radius: var(--radius-lg);
    color: var(--text-secondary);
    font-weight: var(--font-weight-medium);
    font-size: 0.875rem;
    cursor: pointer;
    transition: all var(--transition);
    text-align: center;
    min-height: 44px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.mobile-nav-btn.active {
    background: var(--accent-yellow);
    color: var(--text-primary);
    border-color: var(--accent-yellow);
    box-shadow: var(--shadow-1);
}

.mobile-nav-btn:hover {
    background: var(--btn-secondary-hover);
    border-color: var(--border-medium);
}

.mobile-nav-btn.active:hover {
    background: var(--accent-yellow-dark);
    border-color: var(--accent-yellow-dark);
}

/* Mobile Tab Content */
.mobile-tab-content {
    display: none;
}

.mobile-tab-content.active {
    display: block;
}

/* Modal Info Grid */
.modal-info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: var(--space-4);
    margin-bottom: var(--space-8);
    padding: var(--space-6);
    background: var(--card-bg);
    border-radius: var(--radius-xl);
    border: 1px solid var(--border-light);
    box-shadow: var(--shadow-1);
}

.modal-info-grid p {
    color: var(--text-primary);
    line-height: 1.6;
    margin: 0;
    background: var(--secondary-bg);
    padding: var(--space-4);
    border-radius: var(--radius-lg);
    border: 1px solid var(--border-light);
    transition: all var(--transition);
}

.modal-info-grid p:hover {
    transform: translateY(-1px);
    box-shadow: var(--shadow-1);
}

.modal-info-grid p strong {
    color: var(--text-primary);
    font-weight: var(--font-weight-medium);
    display: inline-block;
    margin-right: var(--space-2);
    min-width: 80px;
}
.modal-subcollection-list .sub-document-item p,
.modal-student-list .student-item p { /* Added student-item */
    color: var(--primary-text); line-height: 1.6; margin: 0 0 8px 0; word-break: break-word;
}
.modal-subcollection-list .sub-document-item p strong,
.modal-student-list .student-item p strong { /* Added student-item */
    color: var(--secondary-text); min-width: 120px; display: inline-block; margin-right: 5px;
}

/* Student List */
.modal-student-list {
    max-height: calc(100vh - 400px);
    overflow-y: auto;
    padding: var(--space-6);
    border: 1px solid var(--border-light);
    border-radius: var(--radius-xl);
    margin-bottom: var(--space-6);
    background: var(--card-bg);
    box-shadow: var(--shadow-1);
}

.student-item {
    background: var(--secondary-bg);
    padding: var(--space-5);
    border-radius: var(--radius-lg);
    margin-bottom: var(--space-4);
    border: 1px solid var(--border-light);
    transition: all var(--transition-smooth);
    position: relative;
    overflow: hidden;
    opacity: 0;
    transform: translateY(15px);
}

.student-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, var(--accent-yellow), var(--accent-yellow-dark));
    transform: scaleX(0);
    transition: transform var(--transition-smooth);
}

.student-item::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(251, 191, 36, 0.05), transparent);
    transition: left var(--transition-slow);
}

.student-item:hover {
    transform: translateY(-3px);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.1);
    border-color: var(--accent-yellow-light);
}

.student-item:hover::before {
    transform: scaleX(1);
}

.student-item:hover::after {
    left: 100%;
}

.student-item h4 {
    margin: 0 0 var(--space-3) 0;
    font-size: 1.125rem;
    color: var(--text-primary);
    font-weight: var(--font-weight-medium);
}

.student-item p {
    margin-bottom: var(--space-1);
    color: var(--text-secondary);
    font-size: 0.875rem;
    line-height: 1.5;
}

.student-item p strong {
    color: var(--text-primary);
    font-weight: var(--font-weight-medium);
}


/* === Teacher Info Page Specifics (teacherInfo.html) === */
.info-page-header {
    display: flex; justify-content: space-between; align-items: center; padding: 20px 0;
    border-bottom: 2px solid var(--border-color); margin-bottom: 30px; width: 100%;
    box-sizing: border-box; flex-wrap: wrap; gap: 15px;
    background: linear-gradient(135deg, var(--secondary-bg) 0%, var(--tertiary-bg) 100%);
    border-radius: var(--border-radius-sm);
    padding: 25px 30px;
    box-shadow: var(--shadow-light);
}
.info-page-header h1 {
    margin: 0; font-size: 2.2em; text-align: left; font-weight: 700; flex-grow: 1;
    background: linear-gradient(135deg, var(--accent-text) 0%, #74b9ff 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    display: flex; align-items: center; gap: 15px;
}
.info-page-header h1::before {
    content: "👨‍🏫";
    font-size: 0.8em;
    -webkit-text-fill-color: initial;
}
.info-page-header .nav-button {
    flex: initial; padding: 12px 24px; border-radius: var(--border-radius-sm);
    background: linear-gradient(135deg, var(--primary-action) 0%, #2980b9 100%);
    border: none; color: white; font-weight: 600;
    transition: all 0.3s ease; box-shadow: 0 4px 15px rgba(52, 152, 219, 0.3);
    text-decoration: none;
}
.info-page-header .nav-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(52, 152, 219, 0.4);
}

.info-page-main {
    display: flex; flex-direction: column; gap: 30px;
    background: var(--secondary-bg); border-radius: var(--border-radius-md);
    padding: 30px; box-shadow: var(--shadow-light);
}
.info-section {
    background: linear-gradient(135deg, var(--tertiary-bg) 0%, var(--secondary-bg) 100%);
    border-radius: var(--border-radius-md);
    padding: 25px;
    border: 1px solid var(--border-color);
    box-shadow: var(--shadow-light);
    position: relative;
    overflow: hidden;
}
.info-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--primary-action), #74b9ff);
}
.info-section h2 {
    font-size: 1.6em; color: var(--accent-text); margin: 0 0 20px 0;
    padding: 0; border: none; font-weight: 600;
    display: flex; align-items: center; gap: 10px;
}
.info-section h2::before {
    font-size: 0.8em;
}
.info-section:first-child h2::before {
    content: "ℹ️";
}
.info-section:nth-child(2) h2::before {
    content: "👥";
}
.info-content-grid, .info-content-list {
    display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 15px;
}
.info-content-grid p, .info-content-list p {
    color: var(--primary-text); line-height: 1.6; margin: 0; word-break: break-word;
    background: linear-gradient(135deg, var(--secondary-bg) 0%, var(--tertiary-bg) 100%);
    padding: 15px; border-radius: var(--border-radius-sm);
    border: 1px solid var(--border-color);
    transition: all 0.3s ease;
    box-shadow: inset 0 2px 5px rgba(0,0,0,0.1);
}
.info-content-grid p:hover, .info-content-list p:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}
.info-content-grid p strong, .info-content-list p strong {
    color: var(--accent-text); min-width: 120px; display: inline-block; margin-right: 8px;
    font-weight: 600;
}
/* Styling for student list on teacherInfo.html */
.info-page-main .student-list-container {
    max-height: 500px;
    overflow-y: auto;
    padding: 15px;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-md);
    background: linear-gradient(135deg, var(--tertiary-bg) 0%, var(--secondary-bg) 100%);
    box-shadow: inset 0 2px 5px rgba(0,0,0,0.1);
}
/* Enhanced student item styling for teacherInfo page */
.info-page-main .student-list-container .student-item {
    background: linear-gradient(135deg, var(--secondary-bg) 0%, var(--tertiary-bg) 100%);
    padding: 20px; border-radius: var(--border-radius-sm);
    margin-bottom: 15px; border: 1px solid var(--border-color);
    transition: all 0.3s ease; box-shadow: var(--shadow-light);
    position: relative; overflow: hidden;
}
.info-page-main .student-list-container .student-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, #2ecc71, #27ae60);
    transform: scaleX(0);
    transition: transform 0.3s ease;
}
.info-page-main .student-list-container .student-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.1);
}
.info-page-main .student-list-container .student-item:hover::before {
    transform: scaleX(1);
}
.info-page-main .student-list-container .student-item h4 {
    margin-top: 0; margin-bottom: 12px; font-size: 1.2em; color: var(--accent-text);
    font-weight: 600; display: flex; align-items: center; gap: 10px;
}
.info-page-main .student-list-container .student-item h4::before {
    content: "👨‍🎓";
    font-size: 0.8em;
}


/* Firestore Value Formatting Styles */
.value-na, .value-null, .value-unknown { font-style: italic; color: var(--secondary-text); }
.value-map, .value-array {
    display: block; margin-left: 20px; padding: 8px; background-color: rgba(0,0,0,0.15);
    border-radius: var(--border-radius-xs); font-size: 0.9em; border: 1px solid var(--border-color);
}
.value-map div, .value-array li { margin-bottom: 4px; padding: 2px 0; }
.value-array { list-style-type: disc; padding-left: 25px; }


/* Scrollbar Styles (WebKit) */
.teacher-list.vertical-scroll::-webkit-scrollbar,
.modal-content::-webkit-scrollbar,
.modal-subcollection-list::-webkit-scrollbar,
.modal-student-list::-webkit-scrollbar, /* Added for student list in modal */
.info-page-main .student-list-container::-webkit-scrollbar { /* Added for student list in teacherInfo.html */
    width: 8px;
}

.teacher-list.vertical-scroll::-webkit-scrollbar-track,
.modal-content::-webkit-scrollbar-track,
.modal-subcollection-list::-webkit-scrollbar-track,
.modal-student-list::-webkit-scrollbar-track, /* Added */
.info-page-main .student-list-container::-webkit-scrollbar-track { /* Added */
    background: var(--secondary-bg); border-radius: 10px;
}

.teacher-list.vertical-scroll::-webkit-scrollbar-thumb,
.modal-content::-webkit-scrollbar-thumb,
.modal-subcollection-list::-webkit-scrollbar-thumb,
.modal-student-list::-webkit-scrollbar-thumb, /* Added */
.info-page-main .student-list-container::-webkit-scrollbar-thumb { /* Added */
    background-color: var(--input-border-color); border-radius: 10px; border: 2px solid var(--secondary-bg);
}

.teacher-list.vertical-scroll::-webkit-scrollbar-thumb:hover,
.modal-content::-webkit-scrollbar-thumb:hover,
.modal-subcollection-list::-webkit-scrollbar-thumb:hover,
.modal-student-list::-webkit-scrollbar-thumb:hover, /* Added */
.info-page-main .student-list-container::-webkit-scrollbar-thumb:hover { /* Added */
    background-color: var(--accent-bg);
}


/* === Menu Button (Fixed) === */
.menu-button {
    position: fixed; bottom: 20px; right: 20px; background-color: var(--tertiary-bg);
    padding: 8px 12px; border-radius: var(--border-radius-xs); font-size: 0.9em;
    box-shadow: var(--shadow-light); z-index: 1000;
}
.menu-button kbd {
    background-color: #566573; padding: 2px 5px; border-radius: 3px;
    border: 1px solid #788895; margin-left: 5px;
}

/* === Mobile Modal Navigation === */
.mobile-modal-nav {
    display: none;
    gap: var(--space-sm);
    margin-bottom: var(--space-lg);
    padding-bottom: var(--space-lg);
    border-bottom: 1px solid var(--divider-color);
}

.mobile-nav-btn {
    flex: 1;
    padding: var(--space-md) var(--space-lg);
    background-color: var(--tertiary-bg);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-sm);
    color: var(--secondary-text);
    font-weight: var(--font-weight-medium);
    font-size: 0.875rem;
    cursor: pointer;
    transition: all var(--transition-fast);
    text-align: center;
    min-height: 44px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--space-xs);
}

.mobile-nav-btn.active {
    background-color: var(--primary-action);
    color: var(--primary-text);
    border-color: var(--primary-action);
    box-shadow: var(--shadow-sm);
}

.mobile-nav-btn:hover {
    background-color: var(--accent-bg);
    border-color: var(--border-hover);
}

.mobile-nav-btn.active:hover {
    background-color: var(--primary-action-hover);
    border-color: var(--primary-action-hover);
}

/* Mobile Tab Content */
.mobile-tab-content {
    display: none;
}

.mobile-tab-content.active {
    display: block;
}

/* Desktop: Show all content, hide mobile navigation */
@media (min-width: 769px) {
    .mobile-modal-nav {
        display: none !important;
    }
    .mobile-tab-content {
        display: block !important;
    }
    .modal-content h3 {
        display: block !important;
    }
}

/* === Mobile Responsiveness === */

/* Mobile Sidebar Toggle */
.mobile-sidebar-toggle {
    display: none;
    position: fixed;
    top: var(--space-4);
    left: var(--space-4);
    z-index: 300;
    background: var(--sidebar-bg);
    color: var(--text-white);
    border: none;
    border-radius: var(--radius-md);
    padding: 0;
    cursor: pointer;
    box-shadow: var(--shadow-lg);
    width: 44px;
    height: 44px;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    box-sizing: border-box;
    transition: all var(--transition);
}

.mobile-sidebar-toggle:hover {
    background: var(--accent-yellow);
    color: var(--text-primary);
    transform: translateY(-1px);
    box-shadow: var(--shadow-xl);
}

/* Mobile Sidebar Close Button */
.mobile-sidebar-close {
    display: none;
    position: absolute;
    top: var(--space-4);
    right: var(--space-4);
    background: var(--btn-danger);
    color: white;
    border: none;
    border-radius: var(--radius-md);
    width: 32px;
    height: 32px;
    font-size: 1rem;
    font-weight: bold;
    cursor: pointer;
    transition: all var(--transition);
    z-index: 10;
    align-items: center;
    justify-content: center;
}

.mobile-sidebar-close:hover {
    background: var(--btn-danger-hover);
    transform: scale(1.1);
}

.sidebar-overlay {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: 150;
    backdrop-filter: blur(2px);
}

/* === Media Queries === */

/* Tablet and small desktop adjustments */
@media (max-width: 1024px) {
    .sidebar {
        width: 260px;
    }

    .main-content {
        margin-left: 260px;
    }

    .header-search {
        max-width: 300px;
    }

    .data-table th,
    .data-table td {
        padding: var(--space-3) var(--space-4);
    }

    .modal-content {
        width: 95%;
        max-width: 900px;
    }
}

/* Mobile landscape and tablet portrait */
@media (max-width: 768px) {
    /* Show mobile sidebar toggle */
    .mobile-sidebar-toggle {
        display: flex !important;
        align-items: center;
        justify-content: center;
        background: var(--sidebar-bg) !important;
        color: var(--text-white) !important;
    }

    /* Show mobile sidebar close button */
    .mobile-sidebar-close {
        display: flex !important;
        align-items: center;
        justify-content: center;
    }

    /* Hide sidebar by default on mobile */
    .sidebar {
        transform: translateX(-100%);
        transition: transform var(--transition);
        width: 100%;
        height: 100vh;
        z-index: 160;
        top: 0;
        left: 0;
        padding: var(--space-6);
    }

    .sidebar.mobile-open {
        transform: translateX(0);
        z-index: 200;
    }

    .sidebar-overlay.active {
        display: block;
    }

    /* Adjust main content for mobile */
    .main-content {
        margin-left: 0;
        padding: var(--space-4);
        width: 100%;
        box-sizing: border-box;
    }

    /* Stack header elements on mobile */
    .admin-header {
        padding: var(--space-4);
    }

    .header-content {
        flex-direction: column;
        gap: var(--space-3);
        align-items: stretch;
    }

    .header-title {
        font-size: 1.25rem;
        text-align: center;
    }

    .header-search-wide {
        max-width: none;
    }

    .header-actions {
        flex-direction: column;
        gap: var(--space-2);
        align-items: stretch;
    }

    .header-actions .section-filter {
        width: 100%;
    }

    .header-actions .button {
        width: 100%;
        justify-content: center;
    }

    /* Make table responsive */
    .data-table-container {
        overflow-x: auto;
    }

    .data-table {
        min-width: 600px;
    }

    .data-table th,
    .data-table td {
        padding: var(--space-2) var(--space-3);
        font-size: 0.8125rem;
    }

    .table-actions {
        gap: var(--space-1);
    }

    .table-action-btn {
        width: 28px;
        height: 28px;
    }

    /* Container adjustments for legacy pages */
    .container, .home-container, .info-page-container {
        padding: 15px; gap: 15px; margin: 0;
    }
    .container {
        flex-direction: column; align-items: stretch;
        min-height: auto; /* Allow natural height on mobile */
    }
    .illustration {
        min-width: auto; max-width: 250px; margin-bottom: 20px;
        flex: initial; align-self: center;
    }
    .form-container { flex: initial; }

    /* Side-by-side layout adjustments */
    .side-by-side-container {
        flex-direction: column;
        height: auto;
        gap: 15px;
    }
    .side-by-side-container .info-section {
        width: 100%;
        height: auto;
        max-height: 60vh; /* Increased for better mobile viewing */
    }

    /* Typography scaling */
    h1 { font-size: 1.8em; }
    .admin-login-container h1 { font-size: 2em; }
    .home-header h1, .info-page-header h1 {
        font-size: 1.8em; text-align: center;
        line-height: 1.2; /* Better line height for mobile */
    }
    .home-main h2, .info-section h2 { font-size: 1.4em; }
    .info-content-grid, .info-content-list { grid-template-columns: 1fr; }

    /* Form and input improvements */
    .input-row { flex-direction: column; gap: 12px; }
    .buttons { flex-direction: column; gap: 12px; }
    button, .nav-button {
        width: 100%; flex: initial;
        min-height: 44px; /* Touch-friendly minimum height */
        font-size: 1em; /* Larger text for mobile */
    }

    /* Header improvements */
    .home-header, .info-page-header {
        flex-direction: column; align-items: stretch;
        padding: 20px 15px; /* Better mobile padding */
    }
    .home-header h1, .info-page-header h1 { margin-bottom: 15px; }
    #logoutButton {
        width: 100%;
        margin-top: 10px; /* Space from title */
        min-height: 44px; /* Touch-friendly */
    }
    .info-page-header .nav-button {
        width: 100%; text-align: center;
        min-height: 44px; /* Touch-friendly */
    }

    /* Navigation improvements */
    .home-nav {
        margin-bottom: 20px;
        flex-direction: column; /* Stack nav buttons on mobile */
        gap: 10px;
    }
    .nav-button {
        padding: 14px 20px; font-size: 1em; margin: 0;
        min-height: 44px; /* Touch-friendly */
        width: 100%; /* Full width on mobile */
    }

    /* Teacher list mobile optimization */
    .teacher-list.vertical-scroll {
        max-height: 60vh; /* Better mobile height */
        grid-template-columns: 1fr;
        gap: 15px;
        padding: 15px;
    }
    .teacher-item-box {
        padding: 20px;
        margin-bottom: 10px; /* Extra spacing between items */
    }
    .teacher-item-box .teacher-name {
        font-size: 1.3em;
        line-height: 1.3; /* Better mobile readability */
    }
    .teacher-item-box .teacher-detail {
        font-size: 0.95em;
        padding: 10px 0; /* More touch-friendly spacing */
    }

    /* Enhanced Mobile Search */
    .search-container {
        margin-bottom: var(--space-4);
        padding: 0 var(--space-2);
    }

    .search-container input {
        padding: var(--space-4) var(--space-5);
        font-size: 1rem;
        border-radius: var(--radius-xl);
        min-height: 48px;
        box-sizing: border-box;
    }

    .detail-card .search-container {
        padding: 0;
        margin-bottom: var(--space-4);
    }

    .detail-card .search-container input {
        padding: var(--space-4) var(--space-4);
        min-height: 48px;
    }

    /* Mobile Teacher Info Compact */
    .teacher-info-compact {
        max-width: 100%;
        margin-bottom: var(--space-4);
    }

    .teacher-info-compact .detail-info-grid {
        grid-template-columns: 1fr;
        gap: var(--space-3);
    }

    .teacher-info-compact h2 {
        font-size: 0.95rem;
        flex-wrap: wrap;
        gap: var(--space-2);
    }

    .teacher-info-toggle {
        font-size: 0.8rem;
        padding: var(--space-2) var(--space-3);
        min-width: 70px;
    }

    .modal-content .search-container {
        padding: 0 var(--space-2);
        margin-bottom: var(--space-4);
    }

    .modal-content .search-container input {
        max-width: 100%;
        padding: var(--space-4) var(--space-4);
        min-height: 48px;
        font-size: 1rem;
    }

    /* Menu button adjustments */
    .menu-button {
        bottom: 15px; right: 15px;
        padding: 10px 15px; font-size: 0.9em;
        min-height: 44px; /* Touch-friendly */
    }

    /* Modal mobile optimization */
    .modal-content {
        padding: 20px; max-height: 95vh; width: 95%; height: auto;
        border-radius: var(--border-radius-sm);
        margin: 2.5vh auto; /* Center vertically with margin */
    }
    .modal-content h2 {
        font-size: 1.9em;
        margin-bottom: 15px; /* Reduced spacing for nav buttons */
        line-height: 1.2;
        padding-right: 80px; /* Adjust for mobile close button */
        padding-top: 15px; /* More space on mobile */
    }



    /* Detail sections mobile visibility - show both sections */
    .detail-section.desktop-visible {
        display: block !important; /* Show both sections on mobile */
    }

    .detail-section {
        display: block;
    }

    /* Mobile teacher detail container */
    .teacher-detail-container {
        height: calc(100vh - 120px);
        padding: var(--space-3);
        background: transparent;
    }

    /* Mobile student section full height */
    .detail-section[data-content="students"].active .detail-card {
        flex: 1;
        display: flex;
        flex-direction: column;
    }

    .detail-section[data-content="students"].active .detail-student-list {
        flex: 1;
        min-height: 300px;
    }

    /* Mobile student item overflow fix */
    .detail-student-list {
        overflow-x: hidden !important;
        width: 100% !important;
        min-width: 0 !important;
        grid-template-columns: 1fr !important;
        gap: var(--space-3) !important;
    }

    .detail-student-list .student-item {
        width: 100% !important;
        min-width: 0 !important;
        overflow: hidden !important;
        flex-shrink: 0 !important;
    }

    .detail-student-list .student-summary {
        flex-wrap: wrap !important;
        gap: var(--space-1) !important;
    }

    .detail-student-list .student-details {
        width: 100% !important;
        min-width: 0 !important;
        overflow: hidden !important;
    }

    /* Mobile navigation buttons for modal (legacy) */
    .mobile-modal-nav {
        display: flex !important; /* Show on mobile */
        gap: 10px;
        margin-bottom: 20px;
        border-bottom: 2px solid var(--border-color);
        padding-bottom: 15px;
    }
    .mobile-nav-btn {
        flex: 1;
        padding: 12px 16px;
        background: var(--tertiary-bg);
        border: 1px solid var(--border-color);
        border-radius: var(--border-radius-sm);
        color: var(--primary-text);
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
        text-align: center;
        min-height: 44px;
        display: flex;
        align-items: center;
        justify-content: center;
    }
    .mobile-nav-btn.active {
        background: var(--primary-action);
        color: white;
        border-color: var(--primary-action);
    }
    .mobile-nav-btn:hover {
        background: var(--accent-bg);
    }
    .mobile-nav-btn.active:hover {
        background: var(--primary-action-hover);
    }
    .modal-content h3 {
        font-size: 1.5em;
        margin-top: 25px; margin-bottom: 20px;
    }
    .modal-info-grid {
        grid-template-columns: 1fr;
        padding: 15px;
        gap: 15px;
    }
    .modal-info-grid p {
        padding: 15px; /* Better mobile padding */
        font-size: 0.95em;
    }
    .modal-info-grid p strong,
    .modal-subcollection-list .sub-document-item p strong,
    .modal-student-list .student-item p strong {
        min-width: 100px;
        display: block; /* Stack labels on mobile */
        margin-bottom: 5px;
    }
    /* Mobile tab content visibility */
    .mobile-tab-content {
        display: none;
    }
    .mobile-tab-content.active {
        display: block;
    }

    /* Hide desktop h3 titles on mobile when using tabs */
    .modal-content h3 {
        display: none;
    }

    .modal-subcollection-list,
    .modal-student-list {
        max-height: calc(100vh - 400px); /* Adjusted for nav buttons */
        padding: 15px;
    }
    .modal-close-btn {
        padding: 12px 20px;
        font-size: 1em;
        top: var(--space-6);
        right: var(--space-4);
        min-height: 48px;
        min-width: 85px;
        position: absolute;
        z-index: 20;
    }

    .settings-modal .modal-close-btn {
        padding: 8px 12px;
        font-size: 0.8rem;
        top: var(--space-3);
        right: var(--space-3);
        min-height: 36px;
        min-width: 55px;
        max-width: 65px;
        position: absolute;
        z-index: 20;
    }

    /* Student item mobile improvements */
    .student-toggle-button {
        width: 90px; /* Slightly larger for mobile */
        height: 36px;
        font-size: 0.9em;
    }
    .student-summary {
        gap: 10px; /* Tighter gap on mobile */
        min-height: 48px; /* Larger touch target */
    }
}

/* Mobile portrait - small screens */
@media (max-width: 480px) {
    /* Login page mobile adjustments */
    .login-page {
        padding: var(--space-4);
    }

    .login-container {
        padding: var(--space-6);
    }

    .login-logo {
        width: 48px;
        height: 48px;
        font-size: 1.5rem;
    }

    .login-header h1 {
        font-size: 1.5rem;
    }

    /* Typography for very small screens */
    h1 { font-size: 1.6em; line-height: 1.2; }
    .admin-login-container h1 { font-size: 1.8em; }
    .home-header h1, .info-page-header h1 {
        font-size: 1.6em;
        text-align: center;
        word-break: break-word; /* Prevent overflow on small screens */
    }
    .home-main h2, .info-section h2 { font-size: 1.2em; }

    /* Container and layout adjustments */
    .container, .home-container, .info-page-container {
        padding: 10px; margin: 0;
    }
    .info-page-header { padding: 15px 10px; }
    .info-page-main { padding: 15px; gap: 15px; }
    .info-section { padding: 15px; }

    /* Form elements - larger touch targets */
    input[type="email"], input[type="password"], input[type="text"],
    #searchTeacherInput {
        font-size: 16px; /* Prevents zoom on iOS */
        padding: 14px 16px;
        min-height: 44px; /* Touch-friendly */
    }
    button, .nav-button {
        font-size: 1em;
        padding: 14px 16px;
        min-height: 48px; /* Larger touch target for small screens */
    }

    /* Teacher list optimizations */
    .teacher-item-box {
        padding: 15px;
        gap: 10px;
        margin-bottom: 12px;
    }
    .teacher-item-box .teacher-name {
        font-size: 1.2em;
        line-height: 1.3;
    }
    .teacher-item-box .teacher-detail {
        font-size: 0.9em;
        padding: 8px 0;
    }

    /* Enhanced Small Screen Search */
    .search-container {
        margin-bottom: var(--space-3);
        padding: 0 var(--space-1);
    }

    .search-container input {
        padding: var(--space-4) var(--space-4);
        font-size: 16px; /* Prevents zoom on iOS */
        border-radius: var(--radius-xl);
        min-height: 50px;
        box-sizing: border-box;
    }

    .detail-card .search-container {
        padding: 0;
        margin-bottom: var(--space-3);
    }

    .detail-card .search-container input {
        padding: var(--space-4) var(--space-3);
        min-height: 50px;
        font-size: 16px;
    }

    /* Small Screen Teacher Info Compact */
    .teacher-info-compact .detail-info-grid {
        grid-template-columns: 1fr;
        gap: var(--space-2);
    }

    .teacher-info-compact h2 {
        font-size: 0.9rem;
        flex-direction: column;
        align-items: flex-start;
        gap: var(--space-2);
    }

    .teacher-info-toggle {
        font-size: 0.75rem;
        padding: var(--space-2);
        min-width: 65px;
        align-self: flex-end;
    }

    /* Ensure teacher info section works on small screens */
    .detail-section[data-content="teacher-info"].active {
        display: block !important;
    }

    .detail-section[data-content="teacher-info"] .teacher-info-toggle {
        display: inline-block !important;
        font-size: 0.75rem;
        padding: var(--space-2);
        min-width: 65px;
    }

    /* Small screen full height */
    .teacher-detail-container {
        height: calc(100vh - 100px);
        padding: var(--space-2);
        background: transparent;
    }

    .detail-section.active {
        flex: 1;
        display: flex;
        flex-direction: column;
    }

    .detail-section[data-content="students"].active .detail-student-list {
        flex: 1;
        min-height: 250px;
    }

    /* Small screen student item overflow fix */
    .detail-student-list {
        overflow-x: hidden !important;
        width: 100% !important;
        min-width: 0 !important;
    }

    .detail-student-list .student-item {
        width: 100% !important;
        min-width: 0 !important;
        overflow: hidden !important;
        padding: var(--space-2);
        flex-shrink: 0 !important;
    }

    .detail-student-list .student-summary {
        flex-wrap: wrap !important;
        gap: var(--space-1) !important;
    }

    .detail-student-list .student-details {
        width: 100% !important;
        min-width: 0 !important;
        overflow: hidden !important;
    }

    .detail-student-list .level-list {
        padding-left: var(--space-2);
        width: 100% !important;
        min-width: 0 !important;
        overflow: hidden !important;
    }

    .teacher-info-compact .detail-info-grid p {
        padding: var(--space-2);
        font-size: 0.75rem;
        flex-direction: column;
        align-items: flex-start;
        gap: var(--space-1);
    }

    .teacher-info-compact .detail-info-grid p strong {
        min-width: auto;
        font-size: 0.75rem;
    }

    .teacher-info-compact .password-container {
        justify-content: flex-start;
        margin-top: var(--space-1);
    }

    .modal-content .search-container {
        padding: 0 var(--space-1);
        margin-bottom: var(--space-3);
    }

    .modal-content .search-container input {
        max-width: 100%;
        padding: var(--space-4) var(--space-3);
        min-height: 50px;
        font-size: 16px;
    }

    /* Modal full-screen on very small devices */
    .modal-content {
        padding: 15px;
        width: 100%;
        height: 100%;
        border-radius: 0;
        margin: 0; /* Full screen */
        max-height: 100vh;
    }
    .modal-content h2 {
        font-size: 1.7em;
        margin-bottom: 10px; /* Reduced for nav buttons */
        padding-right: 70px; /* Space for close button */
        padding-top: 20px; /* More space from top on small screens */
    }

    /* Mobile navigation for small screens */
    .mobile-modal-nav {
        gap: 8px;
        margin-bottom: 15px;
        padding-bottom: 12px;
    }
    .mobile-nav-btn {
        padding: 10px 14px;
        font-size: 0.9em;
        min-height: 44px;
    }

    /* Hide h3 titles on small screens when using tabs */
    .modal-content h3 {
        display: none;
    }
    .modal-info-grid {
        padding: 10px;
        gap: 10px;
    }
    .modal-info-grid p {
        padding: 12px;
        font-size: 0.9em;
    }
    .modal-subcollection-list,
    .modal-student-list {
        max-height: calc(100vh - 250px); /* Adjusted for nav buttons */
        padding: 10px;
    }
    .modal-close-btn {
        padding: 14px 18px;
        font-size: 1.1em;
        top: var(--space-4);
        right: var(--space-3);
        min-width: 90px;
        min-height: 50px;
        position: absolute;
        z-index: 20;
    }

    .settings-modal .modal-close-btn {
        padding: 6px 10px;
        font-size: 0.75rem;
        top: var(--space-2);
        right: var(--space-2);
        min-height: 32px;
        min-width: 50px;
        max-width: 60px;
        position: absolute;
        z-index: 20;
    }

    /* Student items for small screens */
    .student-toggle-button {
        width: 85px;
        height: 40px; /* Larger for easier tapping */
        font-size: 0.85em;
    }
    .student-summary {
        gap: 8px;
        min-height: 50px; /* Larger touch area */
        padding: 5px 0;
        grid-template-columns: 1fr auto;
    }
    .student-summary h4 {
        font-size: 1.05em;
        line-height: 1.2;
    }

    /* Show smaller progress indicator on small screens */
    .student-progress {
        display: flex;
    }

    .circular-progress {
        width: 45px;
        height: 45px;
    }

    .progress-ring {
        width: 45px;
        height: 45px;
    }

    .progress-ring circle {
        r: 18;
        cx: 22.5;
        cy: 22.5;
    }

    .progress-text {
        font-size: 0.6rem;
    }

    .progress-text .percentage {
        font-size: 0.7rem;
    }

    .progress-text .label {
        font-size: 0.5rem;
    }

    /* Adjust student info for mobile */
    .student-stats {
        gap: 2px;
    }

    .student-stats span {
        font-size: 0.7rem;
    }

    /* Menu button for small screens */
    .menu-button {
        bottom: 10px; right: 10px;
        padding: 12px 14px;
        font-size: 0.85em;
        min-height: 44px;
        min-width: 44px;
    }

    /* Level info container adjustments */
    .level-info-container {
        padding: 8px 12px;
        margin-top: 10px;
        margin-bottom: 10px;
    }
    .level-info-container h4 {
        font-size: 0.95em;
        margin-bottom: 8px;
    }
    .level-list li {
        font-size: 0.9em;
        padding: 4px 0;
    }
}

/* Extra small screens and landscape phones */
@media (max-width: 360px) {
    /* Even more compact for very small screens */
    .container, .home-container, .info-page-container {
        padding: 8px;
    }
    .home-header h1, .info-page-header h1 {
        font-size: 1.4em;
    }
    .modal-content h2 {
        font-size: 1.5em;
        padding-right: 60px; /* Space for close button */
        padding-top: 25px; /* Extra space from top */
    }
    .teacher-item-box {
        padding: 12px;
    }
    .student-toggle-button {
        width: 75px;
        height: 38px;
        font-size: 0.8em;
    }

    /* Adjust mobile toggle for very small screens */
    .mobile-sidebar-toggle {
        top: var(--space-3);
        left: var(--space-3);
        width: 40px;
        height: 40px;
        font-size: 1.1rem;
    }
}

/* Landscape orientation optimizations */
@media (max-height: 500px) and (orientation: landscape) {
    .modal-content {
        height: 100%;
        max-height: 100vh;
        overflow-y: auto;
    }
    .modal-subcollection-list,
    .modal-student-list {
        max-height: calc(100vh - 150px);
    }
    .teacher-list.vertical-scroll {
        max-height: 70vh;
    }
    .side-by-side-container .info-section {
        max-height: 80vh;
    }


}

/* === Responsive Alignment Fixes === */
/* Ensure consistent alignment across all screen sizes */
@media (min-width: 769px) and (max-width: 1024px) {
    .main-content {
        width: calc(100% - 260px);
        margin-left: 260px;
    }

    .modal-close-btn {
        top: var(--space-6);
        right: var(--space-6);
        position: absolute;
        z-index: 20;
    }

    .settings-modal .modal-close-btn {
        top: var(--space-4);
        right: var(--space-4);
        position: absolute;
        z-index: 20;
    }
}

@media (min-width: 1025px) {
    .modal-close-btn {
        top: var(--space-6);
        right: var(--space-6);
        position: absolute;
        z-index: 20;
    }

    .settings-modal .modal-close-btn {
        top: var(--space-4);
        right: var(--space-4);
        position: absolute;
        z-index: 20;
    }
}

/* Fix for very wide screens */
@media (min-width: 1400px) {
    .modal-content {
        max-width: 1200px;
    }
}

/* === Student Details Page Styles === */
.student-details-content {
    padding: var(--space-6);
}

.student-header-card {
    background: var(--secondary-bg);
    border-radius: var(--radius-lg);
    padding: var(--space-6);
    margin-bottom: var(--space-6);
    display: flex;
    align-items: center;
    gap: var(--space-4);
    box-shadow: var(--shadow-1);
    border: 1px solid var(--border-light);
}

.student-avatar {
    width: 80px;
    height: 80px;
    background: var(--accent-yellow);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.avatar-icon {
    font-size: 2rem;
    color: var(--primary-bg);
}

.student-info {
    flex: 1;
}

.student-name {
    margin: 0 0 var(--space-2) 0;
    font-size: 1.75rem;
    font-weight: var(--font-weight-bold);
    color: var(--text-primary);
}

.student-id,
.student-email {
    margin: var(--space-1) 0;
    color: var(--text-secondary);
    font-size: 0.9rem;
}

.details-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--space-6);
    margin-bottom: var(--space-6);
}

.info-card {
    background: var(--secondary-bg);
    border-radius: var(--radius-lg);
    padding: var(--space-6);
    box-shadow: var(--shadow-1);
    border: 1px solid var(--border-light);
}

.card-title {
    margin: 0 0 var(--space-4) 0;
    font-size: 1.25rem;
    font-weight: var(--font-weight-semibold);
    color: var(--text-primary);
    padding-bottom: var(--space-3);
    border-bottom: 2px solid var(--accent-yellow);
}

.info-list {
    display: flex;
    flex-direction: column;
    gap: var(--space-3);
}

.info-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--space-2) 0;
    border-bottom: 1px solid var(--border-light);
}

.info-item:last-child {
    border-bottom: none;
}

.info-label {
    font-weight: var(--font-weight-medium);
    color: var(--text-secondary);
    font-size: 0.9rem;
}

.info-value {
    color: var(--text-primary);
    font-weight: var(--font-weight-medium);
    text-align: right;
    font-size: 0.9rem;
}

.progress-levels {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
    gap: var(--space-4);
    margin-top: var(--space-4);
}

.level-card {
    background: var(--tertiary-bg);
    border-radius: var(--radius-md);
    padding: var(--space-4);
    text-align: center;
    border: 2px solid var(--border-light);
    transition: all var(--transition);
}

.level-card.completed {
    background: var(--success-bg);
    border-color: var(--success-color);
    color: var(--success-color);
}

.level-card.not-finished {
    background: var(--error-bg);
    border-color: var(--error-color);
    color: var(--error-color);
}

.level-number {
    font-size: 1.25rem;
    font-weight: var(--font-weight-bold);
    margin-bottom: var(--space-2);
}

.level-status {
    font-size: 0.8rem;
    font-weight: var(--font-weight-semibold);
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-bottom: var(--space-1);
}

.level-score {
    font-size: 0.75rem;
    color: var(--text-secondary);
    margin-top: var(--space-1);
    font-weight: var(--font-weight-medium);
}

.level-date {
    font-size: 0.7rem;
    color: var(--text-muted);
    margin-top: var(--space-1);
    font-style: italic;
}

/* Enhanced Back to Students Button */
.button.secondary {
    display: inline-flex;
    align-items: center;
    gap: var(--space-2);
    background: linear-gradient(135deg, var(--accent-yellow) 0%, #f39c12 100%);
    color: var(--primary-bg);
    padding: var(--space-3) var(--space-5);
    border: none;
    border-radius: var(--radius-lg);
    text-decoration: none;
    font-weight: var(--font-weight-semibold);
    font-size: 0.95rem;
    transition: all var(--transition);
    box-shadow: 0 2px 8px rgba(255, 193, 7, 0.3);
    position: relative;
    overflow: hidden;
}

.button.secondary::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.button.secondary:hover::before {
    left: 100%;
}

.button.secondary:hover {
    background: linear-gradient(135deg, #f39c12 0%, var(--accent-yellow) 100%);
    color: var(--primary-bg);
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(255, 193, 7, 0.4);
}

.button.secondary:active {
    transform: translateY(0);
    box-shadow: 0 2px 8px rgba(255, 193, 7, 0.3);
}

.button.danger {
    display: inline-flex;
    align-items: center;
    gap: var(--space-2);
    background: var(--btn-danger);
    color: var(--text-white);
    border: 1px solid var(--btn-danger);
    padding: var(--space-2) var(--space-4);
    border-radius: var(--radius-md);
    font-size: 0.875rem;
    font-weight: var(--font-weight-medium);
    text-decoration: none;
    transition: all var(--transition);
    cursor: pointer;
    box-shadow: 0 2px 8px rgba(220, 53, 69, 0.3);
}

.button.danger:hover {
    background: var(--btn-danger-hover);
    border-color: var(--btn-danger-hover);
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(220, 53, 69, 0.4);
}

.button.danger:active {
    transform: translateY(0);
    box-shadow: 0 2px 8px rgba(220, 53, 69, 0.3);
}

.loading-message {
    text-align: center;
    padding: var(--space-8);
    color: var(--text-secondary);
    font-size: 1.1rem;
}

.error-message {
    background: var(--error-bg);
    color: var(--error-color);
    padding: var(--space-4);
    border-radius: var(--radius-md);
    margin: var(--space-4) 0;
    border: 1px solid var(--error-color);
}

/* ... (all existing CSS) ... */

/* === Student Item Expand/Collapse Styling === */
.student-item { /* Existing class, ensure it has some bottom margin if not already */
    background-color: var(--tertiary-bg);
    padding: 10px 15px; /* Adjusted padding for a bit more space */
    border-radius: var(--border-radius-xs);
    margin-bottom: 10px;
    border: 1px solid var(--accent-bg); /* Subtle border */
}

.student-summary {
    display: grid;
    grid-template-columns: 1fr auto auto;
    align-items: center;
    gap: var(--space-3);
    cursor: default;
    min-height: 60px;
    padding: var(--space-2) 0;
}

/* Student Info Section */
.student-info h4 {
    margin: 0 0 var(--space-1) 0;
    color: var(--text-primary);
    font-size: 1rem;
    font-weight: var(--font-weight-semibold);
}

.student-stats {
    display: flex;
    flex-direction: column;
    gap: var(--space-1);
    text-align: left;
}

.student-stats span {
    font-size: 0.75rem;
    color: var(--text-secondary);
}

.total-score {
    font-weight: var(--font-weight-medium);
    color: #e67e22 !important;
}

.levels-completed {
    color: var(--text-muted) !important;
}

/* Circular Progress Indicator */
.student-progress {
    display: flex;
    align-items: center;
    justify-content: center;
}

.circular-progress {
    position: relative;
    width: 60px;
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.progress-ring {
    transform: rotate(-90deg);
}

.progress-ring-circle {
    transition: stroke-dashoffset 0.5s ease-in-out;
}

.progress-ring-progress {
    transition: stroke-dashoffset 0.8s ease-in-out;
    stroke-linecap: round;
}

.progress-text {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    text-align: center;
    font-size: 0.7rem;
    line-height: 1.1;
}

.progress-text .percentage {
    display: block;
    font-weight: var(--font-weight-semibold);
    color: var(--text-primary);
    font-size: 0.8rem;
}

.progress-text .label {
    display: block;
    color: var(--text-secondary);
    font-size: 0.6rem;
    margin-top: -2px;
}

.student-summary h4 {
    margin: 0;
    font-size: 1.1em;
    color: var(--accent-text);
    font-weight: 600;
    line-height: 1.2; /* Tight line height to prevent wrapping issues */
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap; /* Prevent text wrapping that causes misalignment */
}

.student-toggle-button {
    background-color: var(--secondary-action);
    color: var(--accent-text);
    border: none;
    padding: 0; /* Remove padding, use fixed dimensions */
    border-radius: var(--border-radius-xs);
    cursor: pointer;
    font-size: 0.85em;
    font-weight: bold;
    transition: background-color 0.2s ease;
    width: 80px; /* Fixed width */
    height: 32px; /* Fixed height */
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.student-toggle-button:hover {
    background-color: var(--secondary-action-hover);
}

.student-details {
    margin-top: 10px; /* Space between summary and details */
    padding-top: 10px;
    border-top: 1px solid var(--accent-bg); /* Separator line */
    animation: fadeInDetails 0.3s ease-out; /* Simple fade-in animation */
}

.student-details p { /* Existing styles from .student-item p can be reused */
    color: var(--primary-text);
    line-height: 1.6;
    margin: 0 0 6px 0; /* Slightly tighter margin */
    word-break: break-word;
    font-size: 0.9em;
}
.student-details p strong { /* Existing styles from .student-item p strong */
    color: var(--secondary-text);
    min-width: auto; /* Allow strong tag to fit content */
    display: inline-block;
    margin-right: 5px;
}


@keyframes fadeInDetails {
    from { opacity: 0; transform: translateY(-5px); }
    to { opacity: 1; transform: translateY(0); }
}

/* === Enhanced Animation Keyframes === */
@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeInDown {
    from {
        opacity: 0;
        transform: translateY(-20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeInLeft {
    from {
        opacity: 0;
        transform: translateX(-20px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes fadeInRight {
    from {
        opacity: 0;
        transform: translateX(20px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes scaleIn {
    from {
        opacity: 0;
        transform: scale(0.9);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideInDown {
    from {
        opacity: 0;
        transform: translateY(-30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes bounceIn {
    0% {
        opacity: 0;
        transform: scale(0.3);
    }
    50% {
        opacity: 1;
        transform: scale(1.05);
    }
    70% {
        transform: scale(0.9);
    }
    100% {
        opacity: 1;
        transform: scale(1);
    }
}

@keyframes float {
    0%, 100% {
        transform: translateY(0px);
    }
    50% {
        transform: translateY(-6px);
    }
}

@keyframes glow {
    0%, 100% {
        box-shadow: 0 0 5px rgba(251, 191, 36, 0.3);
    }
    50% {
        box-shadow: 0 0 20px rgba(251, 191, 36, 0.6);
    }
}

@keyframes spin {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}

@keyframes typing {
    from {
        width: 0;
    }
    to {
        width: 100%;
    }
}

@keyframes blink {
    0%, 50% {
        opacity: 1;
    }
    51%, 100% {
        opacity: 0;
    }
}

@keyframes slideInFromLeft {
    from {
        transform: translateX(-100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes slideInFromRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes progressFill {
    from {
        width: 0%;
    }
    to {
        width: var(--progress-width, 100%);
    }
}

@keyframes heartbeat {
    0%, 100% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.1);
    }
}

/* === Animation Utility Classes === */
.animate-fade-in {
    animation: fadeIn 0.5s var(--transition-smooth) forwards;
}

.animate-fade-in-up {
    animation: fadeInUp 0.6s var(--transition-smooth) forwards;
}

.animate-fade-in-down {
    animation: fadeInDown 0.6s var(--transition-smooth) forwards;
}

.animate-fade-in-left {
    animation: fadeInLeft 0.6s var(--transition-smooth) forwards;
}

.animate-fade-in-right {
    animation: fadeInRight 0.6s var(--transition-smooth) forwards;
}

.animate-scale-in {
    animation: scaleIn 0.4s var(--transition-bounce) forwards;
}

.animate-slide-in-up {
    animation: slideInUp 0.5s var(--transition-smooth) forwards;
}

.animate-slide-in-down {
    animation: slideInDown 0.5s var(--transition-smooth) forwards;
}

.animate-bounce-in {
    animation: bounceIn 0.8s var(--transition-bounce) forwards;
}

.animate-float {
    animation: float 3s ease-in-out infinite;
}

.animate-glow {
    animation: glow 2s ease-in-out infinite;
}

.animate-spin {
    animation: spin 1s linear infinite;
}

.animate-heartbeat {
    animation: heartbeat 1.5s ease-in-out infinite;
}

/* Staggered animations for lists */
.animate-stagger > *:nth-child(1) { animation-delay: 0.1s; }
.animate-stagger > *:nth-child(2) { animation-delay: 0.2s; }
.animate-stagger > *:nth-child(3) { animation-delay: 0.3s; }
.animate-stagger > *:nth-child(4) { animation-delay: 0.4s; }
.animate-stagger > *:nth-child(5) { animation-delay: 0.5s; }
.animate-stagger > *:nth-child(6) { animation-delay: 0.6s; }
.animate-stagger > *:nth-child(7) { animation-delay: 0.7s; }
.animate-stagger > *:nth-child(8) { animation-delay: 0.8s; }
.animate-stagger > *:nth-child(9) { animation-delay: 0.9s; }
.animate-stagger > *:nth-child(10) { animation-delay: 1.0s; }

/* Loading states */
.loading-skeleton {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: shimmer 1.5s infinite;
    border-radius: var(--radius-md);
}

.loading-spinner {
    width: 20px;
    height: 20px;
    border: 2px solid var(--border-light);
    border-top: 2px solid var(--accent-yellow);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    display: inline-block;
}

.loading-dots {
    display: inline-flex;
    gap: 4px;
}

.loading-dots span {
    width: 6px;
    height: 6px;
    background: var(--accent-yellow);
    border-radius: 50%;
    animation: bounce 1.4s ease-in-out infinite both;
}

.loading-dots span:nth-child(1) { animation-delay: -0.32s; }
.loading-dots span:nth-child(2) { animation-delay: -0.16s; }

@keyframes bounce {
    0%, 80%, 100% {
        transform: scale(0);
    }
    40% {
        transform: scale(1);
    }
}

/* Hover animations */
.hover-lift {
    transition: transform var(--transition-fast), box-shadow var(--transition-fast);
}

.hover-lift:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.hover-scale {
    transition: transform var(--transition-fast);
}

.hover-scale:hover {
    transform: scale(1.05);
}

.hover-glow {
    transition: box-shadow var(--transition-smooth);
}

.hover-glow:hover {
    box-shadow: 0 0 20px rgba(251, 191, 36, 0.4);
}

/* Focus animations */
.focus-ring {
    transition: box-shadow var(--transition-fast);
}

/* Animation performance optimizations */
.animate-optimized {
    will-change: transform, opacity;
    backface-visibility: hidden;
    perspective: 1000px;
}

.animate-optimized.animate-complete {
    will-change: auto;
}

/* Prevent animation conflicts */
.no-animation {
    animation: none !important;
    transition: none !important;
}

/* Smooth entrance animations */
.entrance-animation {
    opacity: 0;
    transform: translateY(20px);
    transition: opacity 0.4s ease-out, transform 0.4s ease-out;
}

.entrance-animation.visible {
    opacity: 1;
    transform: translateY(0);
}

/* Hover animation base */
.hover-base {
    transition: transform 0.2s ease-out, box-shadow 0.2s ease-out;
}

.hover-base:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.focus-ring:focus {
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* Page transition classes */
.page-enter {
    opacity: 0;
    transform: translateY(20px);
}

.page-enter-active {
    opacity: 1;
    transform: translateY(0);
    transition: opacity var(--transition-slow), transform var(--transition-slow);
}

.page-exit {
    opacity: 1;
    transform: translateY(0);
}

.page-exit-active {
    opacity: 0;
    transform: translateY(-20px);
    transition: opacity var(--transition-fast), transform var(--transition-fast);
}

/* === Additional Mobile-Friendly Enhancements === */

/* Student Name Link Styling */
.student-name-link {
    color: var(--accent-yellow);
    text-decoration: none;
    font-weight: var(--font-weight-medium);
    transition: all var(--transition);
    cursor: pointer;
}

.student-name-link:hover {
    color: var(--accent-yellow-dark);
    text-decoration: underline;
}

/* Edit Form Styling */
.edit-form {
    display: flex;
    flex-direction: column;
    gap: var(--space-4);
    width: 100%;
    max-width: 400px;
}

.edit-form .form-group {
    display: flex;
    flex-direction: column;
    gap: var(--space-2);
    opacity: 0;
    transform: translateY(20px);
    animation: fadeInUp 0.4s ease-out forwards;
}

/* Staggered animation delays for form fields */
.edit-form .form-group:nth-child(1) { animation-delay: 0.1s; }
.edit-form .form-group:nth-child(2) { animation-delay: 0.2s; }
.edit-form .form-group:nth-child(3) { animation-delay: 0.3s; }
.edit-form .form-group:nth-child(4) { animation-delay: 0.4s; }
.edit-form .form-actions {
    animation: fadeInUp 0.4s ease-out forwards;
    animation-delay: 0.5s;
    opacity: 0;
    transform: translateY(20px);
}

.edit-form .form-group label {
    font-size: 0.875rem;
    font-weight: var(--font-weight-medium);
    color: var(--text-primary);
}

.edit-form .form-group input,
.edit-form .form-group select {
    width: 100%;
    padding: var(--space-3) var(--space-4);
    border: 1px solid var(--border-light);
    border-radius: var(--radius-md);
    background: var(--secondary-bg);
    font-size: 0.875rem;
    color: var(--text-primary);
    transition: all 0.3s ease;
    box-sizing: border-box;
    font-family: var(--font-google);
    transform: translateY(0);
}

.edit-form .form-group input:hover,
.edit-form .form-group select:hover {
    border-color: var(--accent-yellow);
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(251, 191, 36, 0.15);
}

.edit-form .form-group select {
    cursor: pointer;
    appearance: none;
    background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6,9 12,15 18,9'%3e%3c/polyline%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: right var(--space-3) center;
    background-size: 16px;
    padding-right: calc(var(--space-4) + 24px);
}

.edit-form .form-group select:hover {
    border-color: var(--border-medium);
    background-color: rgba(251, 191, 36, 0.05);
}

.edit-form .form-group input:focus,
.edit-form .form-group select:focus {
    outline: none;
    border-color: var(--accent-yellow);
    box-shadow: 0 0 0 3px rgba(251, 191, 36, 0.15);
}

.edit-form .form-group select option {
    background: var(--secondary-bg);
    color: var(--text-primary);
    padding: var(--space-2);
    border: none;
}

/* Section dropdown specific styling */
.edit-form .form-group select#editSection {
    background-color: var(--secondary-bg);
    border: 1px solid var(--border-light);
    position: relative;
}

.edit-form .form-group select#editSection:hover {
    border-color: var(--accent-yellow);
    background-color: rgba(251, 191, 36, 0.08);
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.edit-form .form-group select#editSection:focus {
    border-color: var(--accent-yellow);
    box-shadow: 0 0 0 3px rgba(251, 191, 36, 0.15);
    background-color: var(--secondary-bg);
}

/* Enhanced form group styling for section */
.edit-form .form-group:has(select#editSection) label {
    color: var(--text-primary);
    font-weight: var(--font-weight-semibold);
    margin-bottom: var(--space-2);
    display: flex;
    align-items: center;
    gap: var(--space-1);
}

.edit-form .form-group:has(select#editSection) label::before {
    content: "📚";
    font-size: 0.875rem;
    opacity: 0.7;
}

/* Mobile responsive adjustments for edit form */
@media (max-width: 768px) {
    .edit-form .form-group select {
        font-size: 16px; /* Prevents zoom on iOS */
        padding: var(--space-4);
        min-height: 44px; /* Touch-friendly */
    }

    .edit-form .form-group select#editSection {
        background-size: 18px;
        padding-right: calc(var(--space-4) + 28px);
    }

    .student-modal-content {
        padding: var(--space-6);
        margin: var(--space-2);
    }

    .edit-form {
        gap: var(--space-5);
    }
}

/* Dark mode support for select dropdown */
@media (prefers-color-scheme: dark) {
    .edit-form .form-group select {
        background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23ffffff' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6,9 12,15 18,9'%3e%3c/polyline%3e%3c/svg%3e");
    }
}

.edit-form .form-actions {
    display: flex;
    gap: var(--space-3);
    justify-content: center;
    margin-top: var(--space-4);
}

.edit-form .form-actions button {
    flex: 1;
    max-width: 120px;
    padding: var(--space-3) var(--space-4);
    font-size: 0.875rem;
    font-weight: var(--font-weight-medium);
    border-radius: var(--radius-md);
    cursor: pointer;
    transition: all 0.3s ease;
    min-height: 40px;
    transform: translateY(0);
    border: none;
}

.edit-form .form-actions button:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.edit-form .form-actions button:active {
    transform: translateY(0);
    transition: all 0.1s ease;
}

/* Improve touch scrolling on mobile devices */
.teacher-list.vertical-scroll,
.modal-subcollection-list,
.modal-student-list,
.info-page-main .student-list-container {
    -webkit-overflow-scrolling: touch; /* Smooth scrolling on iOS */
    scroll-behavior: smooth;
}

/* Prevent text selection on interactive elements for better mobile UX */
button, .nav-button, .teacher-item-box, .student-toggle-button, .modal-close-btn {
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    -webkit-tap-highlight-color: transparent; /* Remove tap highlight on mobile */
}

/* Improve focus states for keyboard navigation */
button:focus, .nav-button:focus, input:focus, .student-toggle-button:focus {
    outline: 2px solid var(--primary-action);
    outline-offset: 2px;
}

/* Better mobile form styling */
@media (max-width: 768px) {
    /* Prevent zoom on input focus for iOS */
    input[type="email"], input[type="password"], input[type="text"], #searchTeacherInput {
        font-size: 16px !important; /* Force 16px to prevent zoom */
    }

    /* Improve button spacing on mobile */
    .buttons button:not(:last-child) {
        margin-bottom: 12px;
    }

    /* Better modal overlay on mobile */
    .modal-overlay {
        padding: 0; /* Remove padding for full-screen modals */
    }

    /* Ensure mobile toggle is properly positioned */
    .mobile-sidebar-toggle {
        position: fixed !important;
        top: var(--space-4) !important;
        left: var(--space-4) !important;
        z-index: 200 !important;
    }

    /* Improve password toggle positioning for mobile */
    .password-container {
        position: relative;
    }
    .password-toggle {
        position: absolute;
        right: 12px;
        top: 50%;
        transform: translateY(-50%);
        background: none;
        border: none;
        color: var(--secondary-text);
        cursor: pointer;
        padding: 8px;
        min-height: 44px; /* Touch-friendly */
        min-width: 44px;
    }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    :root {
        --border-color: #ffffff;
        --input-border-color: #ffffff;
        --primary-text: #ffffff;
        --secondary-text: #cccccc;
    }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
    *, *::before, *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }

    html {
        scroll-behavior: auto;
    }
}

/* Enhanced loading states */
.loading-text {
    position: relative;
    color: var(--text-muted);
}

.loading-text::after {
    content: '';
    display: inline-block;
    width: 4px;
    height: 1em;
    background: var(--accent-yellow);
    margin-left: 4px;
    animation: blink 1s infinite;
}

/* Smooth page transitions */
.page-transition {
    opacity: 0;
    transform: translateY(20px);
    transition: opacity var(--transition-slow), transform var(--transition-slow);
}

.page-transition.loaded {
    opacity: 1;
    transform: translateY(0);
}

/* Enhanced button press feedback */
.btn-primary:active,
.btn-secondary:active,
.btn-danger:active {
    transform: translateY(1px) scale(0.98);
    transition: transform 0.1s ease;
}

/* Improved focus states for accessibility */
.focus-visible {
    outline: 2px solid var(--accent-yellow);
    outline-offset: 2px;
    border-radius: var(--radius-sm);
}

/* === Level Progress Styling === */
.level-info-container {
    background-color: var(--accent-bg);
    border-radius: var(--border-radius-xs);
    padding: 10px 15px;
    margin-top: 15px;
    margin-bottom: 15px;
    border: 1px solid var(--border-color);
}

.level-info-container h4 {
    color: var(--accent-text);
    font-size: 1em;
    margin-top: 0;
    margin-bottom: 10px;
    border-bottom: 1px dashed rgba(255, 255, 255, 0.2);
    padding-bottom: 8px;
}

.level-list {
    list-style: none; /* Remove default bullet points */
    padding: 0;
    margin: 0;
}

.level-list li {
    color: var(--primary-text);
    font-size: 0.95em;
    padding: 5px 0;
    margin-bottom: 5px;
    border-bottom: 1px dotted rgba(255, 255, 255, 0.1);
}

.level-list li:last-child {
    border-bottom: none; /* No border for the last item */
    margin-bottom: 0;
}

.level-list li strong {
    color: var(--accent-text);
    margin-right: 8px;
}

.side-by-side-container {
    display: flex;
    justify-content: space-between;
    gap: 20px;
    height: calc(100vh - 200px); /* Adjust based on header height */
}

.side-by-side-container .info-section {
    width: 48%;
    height: 100%;
    overflow-y: auto;
}

/* === Mobile Utility Classes === */

/* Hide elements on mobile */
@media (max-width: 768px) {
    .hide-mobile {
        display: none !important;
    }
}

/* Show elements only on mobile */
.show-mobile {
    display: none !important;
}

@media (max-width: 768px) {
    .show-mobile {
        display: block !important;
    }
    .show-mobile.inline {
        display: inline !important;
    }
    .show-mobile.flex {
        display: flex !important;
    }
}

/* Mobile-specific spacing utilities */
@media (max-width: 768px) {
    .mobile-mt-1 { margin-top: 0.25rem !important; }
    .mobile-mt-2 { margin-top: 0.5rem !important; }
    .mobile-mt-3 { margin-top: 1rem !important; }
    .mobile-mb-1 { margin-bottom: 0.25rem !important; }
    .mobile-mb-2 { margin-bottom: 0.5rem !important; }
    .mobile-mb-3 { margin-bottom: 1rem !important; }
    .mobile-p-1 { padding: 0.25rem !important; }
    .mobile-p-2 { padding: 0.5rem !important; }
    .mobile-p-3 { padding: 1rem !important; }
}

/* Touch-friendly sizing utilities */
.touch-target {
    min-height: 44px;
    min-width: 44px;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Mobile text utilities */
@media (max-width: 768px) {
    .mobile-text-center { text-align: center !important; }
    .mobile-text-left { text-align: left !important; }
    .mobile-text-sm { font-size: 0.875rem !important; }
    .mobile-text-lg { font-size: 1.125rem !important; }
}

/* Responsive grid utilities */
@media (max-width: 768px) {
    .mobile-grid-1 { grid-template-columns: 1fr !important; }
    .mobile-flex-col { flex-direction: column !important; }
    .mobile-w-full { width: 100% !important; }
}

/* === Level Management Styles === */

/* Levels Container */
.levels-container {
    padding: 20px;
}

/* Section Selector for Level Management */
.section-selector-container {
    background: var(--card-bg);
    border: 1px solid var(--border-light);
    border-radius: var(--radius-lg);
    padding: var(--space-6);
    margin-bottom: var(--space-6);
    box-shadow: var(--shadow-sm);
}

.section-selector-header h3 {
    margin-bottom: var(--space-4);
    color: var(--text-primary);
    font-weight: var(--font-weight-semibold);
    font-size: 1.125rem;
}

.section-select {
    width: 100%;
    max-width: 400px;
    padding: var(--space-3) var(--space-4);
    border: 1px solid var(--border-light);
    border-radius: var(--radius-md);
    background: var(--secondary-bg);
    color: var(--text-primary);
    font-size: 0.875rem;
    transition: all var(--transition);
    cursor: pointer;
}

.section-select:focus {
    outline: none;
    border-color: var(--border-focus);
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.section-select:hover {
    border-color: var(--accent-yellow);
}

/* Save Level Changes Button */
#saveLevelChanges {
    display: none; /* Hidden by default, shown when section is selected */
}

.levels-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 20px;
    margin-top: 20px;
}

/* Level Card */
.level-card {
    background: var(--card-bg);
    border: 1px solid var(--border-light);
    border-radius: 12px;
    padding: 20px;
    transition: all 0.3s ease;
    position: relative;
}

.level-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    border-color: var(--accent-yellow);
}

.level-card-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 15px;
}

.level-card-title {
    font-size: 18px;
    font-weight: 600;
    color: var(--text-primary);
    margin: 0;
}

.level-card-status {
    font-size: 12px;
    font-weight: 500;
    padding: 4px 8px;
    border-radius: 6px;
    display: flex;
    align-items: center;
    gap: 4px;
}

.level-card-status.unlocked {
    background: rgba(46, 204, 113, 0.1);
    color: var(--success-color);
    border: 1px solid rgba(46, 204, 113, 0.3);
}

.level-card-status.locked {
    background: rgba(231, 76, 60, 0.1);
    color: var(--error-color);
    border: 1px solid rgba(231, 76, 60, 0.3);
}

.level-card-description {
    color: var(--text-secondary);
    font-size: 14px;
    line-height: 1.5;
    margin-bottom: 20px;
    min-height: 40px;
}

.level-card-toggle {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding-top: 15px;
    border-top: 1px solid var(--border-light);
}

.toggle-label {
    font-size: 14px;
    font-weight: 500;
    color: var(--text-primary);
    margin-left: 10px;
}

/* Toggle Switch */
.toggle-switch {
    position: relative;
    display: inline-block;
    width: 50px;
    height: 24px;
    cursor: pointer;
}

.toggle-switch input {
    position: absolute;
    opacity: 0;
    width: 100%;
    height: 100%;
    margin: 0;
    cursor: pointer;
    z-index: 2;
}

.toggle-slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #ccc;
    transition: 0.3s;
    border-radius: 24px;
    z-index: 1;
    pointer-events: none;
}

.toggle-slider:before {
    position: absolute;
    content: "";
    height: 18px;
    width: 18px;
    left: 3px;
    bottom: 3px;
    background-color: white;
    transition: 0.3s;
    border-radius: 50%;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.toggle-switch input:checked + .toggle-slider {
    background-color: var(--accent-yellow);
}

.toggle-switch input:focus + .toggle-slider {
    box-shadow: 0 0 1px var(--accent-yellow);
}

.toggle-switch input:checked + .toggle-slider:before {
    transform: translateX(26px);
}

/* Mobile Responsiveness for Levels */
@media (max-width: 768px) {
    .levels-grid {
        grid-template-columns: 1fr;
        gap: 15px;
    }

    .level-card-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }

    .level-card-toggle {
        flex-direction: row-reverse;
        justify-content: flex-end;
        gap: 10px;
    }
}

/* === Section Management Styles === */

/* Section Filter */
.section-filter {
    padding: 8px 12px;
    border: 1px solid var(--border-light);
    border-radius: 6px;
    background: var(--card-bg);
    color: var(--text-primary);
    font-size: 14px;
    margin-right: 10px;
    min-width: 150px;
}

.section-filter:focus {
    outline: none;
    border-color: var(--border-focus);
    box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1);
}

/* Sections Container */
.sections-container {
    padding: 20px;
}

.sections-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 20px;
    margin-top: 20px;
}

/* Section Card */
.section-card {
    background: var(--card-bg);
    border: 1px solid var(--border-light);
    border-radius: 12px;
    padding: 20px;
    transition: all 0.3s ease;
    position: relative;
}

.section-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    border-color: var(--accent-yellow);
}

.section-card[style*="cursor: pointer"]:hover {
    background: linear-gradient(135deg, var(--card-bg) 0%, rgba(255, 193, 7, 0.05) 100%);
}

.section-card[style*="cursor: pointer"]:hover .section-card-title {
    color: var(--accent-yellow);
}

.section-card[style*="cursor: pointer"]:active {
    transform: translateY(0px);
    transition: transform 0.1s ease;
}

.section-card-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 15px;
}

.section-card-title {
    font-size: 18px;
    font-weight: 600;
    color: var(--text-primary);
    margin: 0;
}

.section-card-actions {
    display: flex;
    gap: 8px;
}

.section-card-actions .button {
    padding: 6px 12px;
    font-size: 12px;
    min-width: auto;
}

.section-card-description {
    color: var(--text-secondary);
    font-size: 14px;
    line-height: 1.5;
    margin-bottom: 15px;
    min-height: 40px;
}

.section-card-stats {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-top: 15px;
    border-top: 1px solid var(--border-light);
}

.section-student-count {
    color: var(--accent-yellow);
    font-weight: 500;
    font-size: 14px;
}

.section-created-date {
    color: var(--text-secondary);
    font-size: 12px;
}

/* Section Modal Styles */
.section-modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.7);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 10000;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.section-modal-overlay.active {
    opacity: 1;
}

.section-modal-container {
    background: var(--card-bg);
    border-radius: 12px;
    padding: 30px;
    width: 90%;
    max-width: 500px;
    position: relative;
    transform: scale(0.9);
    transition: transform 0.3s ease;
}

.section-modal-overlay.active .section-modal-container {
    transform: scale(1);
}

.section-modal-close-btn {
    position: absolute;
    top: 15px;
    right: 15px;
    background: none;
    border: none;
    color: var(--secondary-text);
    font-size: 16px;
    cursor: pointer;
    padding: 5px;
    border-radius: 4px;
    transition: all 0.2s ease;
}

.section-modal-close-btn:hover {
    background: var(--hover-bg);
    color: var(--primary-text);
}

.section-form {
    margin-top: 20px;
}

.section-form .input-wrapper {
    margin-bottom: 20px;
}

.section-form label {
    display: block;
    margin-bottom: 8px;
    color: var(--primary-text);
    font-weight: 500;
    font-size: 14px;
}

.section-form input,
.section-form textarea,
.section-select {
    width: 100%;
    padding: 12px;
    border: 1px solid var(--border-color);
    border-radius: 8px;
    background: var(--input-bg);
    color: var(--primary-text);
    font-size: 14px;
    transition: all 0.2s ease;
    box-sizing: border-box;
}

.section-form textarea {
    resize: vertical;
    min-height: 80px;
    max-height: 150px;
}

.section-form input:focus,
.section-form textarea:focus,
.section-select:focus {
    outline: none;
    border-color: var(--accent-color);
    box-shadow: 0 0 0 2px rgba(74, 144, 226, 0.2);
}

.section-error {
    color: var(--error-color);
    font-size: 14px;
    margin-top: 10px;
    padding: 10px;
    background: rgba(220, 53, 69, 0.1);
    border: 1px solid rgba(220, 53, 69, 0.3);
    border-radius: 6px;
}

/* Empty State */
.sections-empty-state {
    text-align: center;
    padding: 60px 20px;
    color: var(--secondary-text);
}

.sections-empty-state h3 {
    color: var(--primary-text);
    margin-bottom: 10px;
}

.sections-empty-state p {
    margin-bottom: 20px;
}

/* Mobile Responsiveness */
@media (max-width: 768px) {
    .sections-grid {
        grid-template-columns: 1fr;
        gap: 15px;
    }

    .section-modal-container {
        width: 95%;
        padding: 20px;
        margin: 20px;
    }

    .section-card-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }

    .section-card-actions {
        width: 100%;
        justify-content: flex-end;
    }

    .section-filter {
        min-width: 120px;
        margin-right: 5px;
    }
}

/* === Question Management Styles === */
.questions-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 2px solid var(--border-light);
}

.questions-header h2 {
    margin: 0;
    color: var(--text-primary);
    font-size: 1.8rem;
    font-weight: var(--font-weight-semibold);
}

/* Level Tabs Container */
.level-tabs-container {
    margin-bottom: 2rem;
    background: var(--card-bg);
    border-radius: var(--radius-xl);
    padding: 1.5rem;
    box-shadow: var(--shadow-1);
    border: 1px solid var(--border-light);
}

.level-tabs-grid {
    display: grid;
    grid-template-columns: repeat(6, 1fr);
    gap: 0.75rem;
    max-width: 100%;
}

.level-tab {
    background: var(--secondary-bg);
    border: 2px solid var(--border-light);
    border-radius: var(--radius-lg);
    padding: 0.75rem 1rem;
    font-size: 0.9rem;
    font-weight: var(--font-weight-medium);
    color: var(--text-secondary);
    cursor: pointer;
    transition: all var(--transition);
    text-align: center;
    min-height: 48px;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    overflow: hidden;
}

.level-tab::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(251, 191, 36, 0.1), transparent);
    transition: left var(--transition-slow);
}

.level-tab:hover {
    border-color: var(--accent-yellow);
    color: var(--text-primary);
    transform: translateY(-2px);
    box-shadow: var(--shadow-2);
}

.level-tab:hover::before {
    left: 100%;
}

.level-tab.active {
    background: var(--accent-yellow);
    border-color: var(--accent-yellow);
    color: var(--primary-bg);
    font-weight: var(--font-weight-semibold);
    box-shadow: var(--shadow-2);
    transform: translateY(-1px);
}

.level-tab.active:hover {
    background: var(--accent-yellow-dark);
    border-color: var(--accent-yellow-dark);
    transform: translateY(-2px);
}

/* Responsive Level Tabs */
@media (max-width: 1200px) {
    .level-tabs-grid {
        grid-template-columns: repeat(4, 1fr);
    }
}

@media (max-width: 768px) {
    .level-tabs-grid {
        grid-template-columns: repeat(3, 1fr);
        gap: 0.5rem;
    }

    .level-tab {
        padding: 0.5rem 0.75rem;
        font-size: 0.8rem;
        min-height: 40px;
    }

    .level-tabs-container {
        padding: 1rem;
    }
}

@media (max-width: 480px) {
    .level-tabs-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    .level-tab {
        padding: 0.5rem;
        font-size: 0.75rem;
        min-height: 36px;
    }
}

.questions-content {
    margin-top: 1.5rem;
}

.no-questions {
    text-align: center;
    padding: 3rem 2rem;
    background: var(--card-bg);
    border-radius: var(--radius-xl);
    border: 2px dashed var(--border-light);
}

.no-questions p {
    margin: 0.5rem 0;
    color: var(--text-secondary);
    font-size: 1.1rem;
}

.question-card {
    background: var(--card-bg);
    border: 1px solid var(--border-light);
    border-radius: var(--radius-xl);
    margin-bottom: 1.5rem;
    padding: 1.5rem;
    box-shadow: var(--shadow-1);
    transition: all var(--transition);
}

.question-card:hover {
    box-shadow: var(--shadow-2);
    transform: translateY(-2px);
    border-color: var(--accent-yellow);
}

.question-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
    padding-bottom: 0.75rem;
    border-bottom: 1px solid var(--border-light);
}

.question-header h4 {
    margin: 0;
    color: var(--text-primary);
    font-size: 1.2rem;
    font-weight: var(--font-weight-medium);
}

.question-actions {
    display: flex;
    gap: 0.5rem;
}

.question-actions .button.small {
    padding: 0.4rem 0.8rem;
    font-size: 0.85rem;
    border-radius: var(--radius-md);
}

.question-content {
    margin-top: 1rem;
}

.question-text {
    margin-bottom: 1rem;
    font-size: 1rem;
    line-height: 1.5;
    color: var(--text-primary);
}

.choices-list {
    margin-top: 1rem;
}

.choice-item {
    display: flex;
    align-items: center;
    padding: 0.5rem 0.75rem;
    margin-bottom: 0.5rem;
    background: var(--secondary-bg);
    border-radius: var(--radius-md);
    border-left: 4px solid var(--border-light);
    transition: all var(--transition);
}

.choice-item.correct {
    background: rgba(46, 204, 113, 0.1);
    border-left-color: #2ecc71;
}

.choice-letter {
    font-weight: var(--font-weight-bold);
    margin-right: 0.75rem;
    color: var(--text-secondary);
    min-width: 20px;
}

.choice-text {
    flex: 1;
    color: var(--text-primary);
}

.correct-indicator {
    color: #2ecc71;
    font-weight: var(--font-weight-bold);
    margin-left: 0.5rem;
}

/* Question Modal Styles */
.question-modal-content {
    max-width: 700px;
    width: 95%;
}

.question-form .form-group {
    margin-bottom: 1.5rem;
}

.question-form label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: var(--font-weight-semibold);
    color: var(--text-primary);
}

.question-form textarea {
    width: 100%;
    padding: 0.75rem;
    border: 2px solid var(--border-light);
    border-radius: var(--radius-md);
    font-size: 1rem;
    font-family: inherit;
    resize: vertical;
    min-height: 80px;
    transition: border-color var(--transition);
    background: var(--secondary-bg);
    color: var(--text-primary);
}

.question-form textarea:focus {
    outline: none;
    border-color: var(--accent-yellow);
    box-shadow: 0 0 0 3px rgba(251, 191, 36, 0.15);
}

.choices-section {
    margin: 2rem 0;
}

.choices-section h4 {
    margin-bottom: 1rem;
    color: var(--text-primary);
    font-size: 1.1rem;
    font-weight: var(--font-weight-medium);
}

.choice-group {
    margin-bottom: 1.5rem;
    padding: 1rem;
    background: var(--secondary-bg);
    border-radius: var(--radius-md);
    border: 1px solid var(--border-light);
}

.choice-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.75rem;
}

.choice-label {
    font-weight: var(--font-weight-semibold);
    color: var(--text-primary);
    margin: 0;
}

.correct-choice {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.9rem;
    color: #2ecc71;
    font-weight: var(--font-weight-semibold);
    margin: 0;
}

.correct-choice input[type="radio"] {
    margin: 0;
    width: auto;
    accent-color: #2ecc71;
}

.choice-input {
    width: 100%;
    padding: 0.75rem;
    border: 2px solid var(--border-light);
    border-radius: var(--radius-md);
    font-size: 1rem;
    transition: border-color var(--transition);
    background: var(--primary-bg);
    color: var(--text-primary);
}

.choice-input:focus {
    outline: none;
    border-color: var(--accent-yellow);
    box-shadow: 0 0 0 3px rgba(251, 191, 36, 0.15);
}

/* Mobile responsiveness for question management */
@media (max-width: 768px) {
    .questions-header {
        flex-direction: column;
        gap: 1rem;
        align-items: stretch;
    }

    .questions-header h2 {
        text-align: center;
        font-size: 1.5rem;
    }

    .question-card {
        padding: 1rem;
    }

    .question-header {
        flex-direction: column;
        gap: 0.75rem;
        align-items: stretch;
    }

    .question-actions {
        justify-content: center;
    }

    .choice-header {
        flex-direction: column;
        gap: 0.5rem;
        align-items: stretch;
    }

    .correct-choice {
        justify-content: center;
    }

    .question-modal-content {
        width: 95%;
        max-width: none;
    }
}
