{"name": "kamusta-po-guro-admin", "version": "2.0.0", "description": "Secure admin dashboard for Kamusta Po Guro educational platform", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "jest", "security-audit": "npm audit", "lint": "eslint .", "build": "npm run security-audit && npm run lint"}, "keywords": ["education", "admin", "dashboard", "secure", "firebase"], "author": "Kamusta Po Guro Team", "license": "MIT", "dependencies": {"express": "^4.18.2", "express-rate-limit": "^7.1.5", "helmet": "^7.1.0", "cors": "^2.8.5", "bcrypt": "^5.1.1", "jsonwebtoken": "^9.0.2", "express-session": "^1.17.3", "express-validator": "^7.0.1", "dotenv": "^16.3.1", "firebase-admin": "^11.11.1", "compression": "^1.7.4", "morgan": "^1.10.0"}, "devDependencies": {"nodemon": "^3.0.2", "jest": "^29.7.0", "eslint": "^8.56.0", "supertest": "^6.3.3"}, "engines": {"node": ">=16.0.0", "npm": ">=8.0.0"}, "repository": {"type": "git", "url": "https://github.com/your-org/kamusta-po-guro-admin.git"}, "bugs": {"url": "https://github.com/your-org/kamusta-po-guro-admin/issues"}, "homepage": "https://github.com/your-org/kamusta-po-guro-admin#readme"}